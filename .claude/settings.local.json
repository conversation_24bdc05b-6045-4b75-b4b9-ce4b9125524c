{"permissions": {"allow": ["<PERSON><PERSON>(pip show:*)", "Bash(grep:*)", "<PERSON><PERSON>(chmod:*)", "<PERSON><PERSON>(mv:*)", "Bash(rm:*)", "<PERSON><PERSON>(python test:*)", "<PERSON><PERSON>(python:*)", "<PERSON><PERSON>(mcp list:*)", "Bash(pip install:*)", "<PERSON><PERSON>(mcp:*)", "WebSearch", "WebFetch(domain:github.com)", "Bash(npx:*)", "WebFetch(domain:mcp.devin.ai)", "Bash(lsof:*)", "WebFetch(domain:mcp.deepwiki.com)", "Bash(npm search:*)", "<PERSON><PERSON>(curl:*)", "Bash(timeout 10s npx -y mcp-deepwiki@latest)", "<PERSON><PERSON>(gtimeout:*)", "<PERSON><PERSON>(pip uninstall:*)", "<PERSON><PERSON>(playwright install:*)", "Bash(git checkout:*)", "<PERSON><PERSON>(docker inspect:*)", "Bash(./docker-test.sh:*)", "Bash(docker build:*)"], "defaultMode": "acceptEdits", "additionalDirectories": ["/Applications/Google Chrome.app/Contents/MacOS"]}}