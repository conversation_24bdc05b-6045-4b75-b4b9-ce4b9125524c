#!/bin/bash
# Quick Docker Browser Control Test Script
# Run this script to build and test the browser manual control feature

set -e

echo "🐳 Agent Zero Browser Manual Control - Docker Test"
echo "================================================="

# Check if Docker is available
if ! command -v docker &> /dev/null; then
    echo "❌ Docker is not installed or not in PATH"
    exit 1
fi

echo "✅ Docker is available"

# Build the image
echo ""
echo "🔨 Building Agent Zero with Browser Manual Control..."
docker build -f docker/run/Dockerfile -t agent-zero-browser-test . --build-arg BRANCH=main

echo ""
echo "✅ Docker image built successfully"

# Run container with required ports
echo ""
echo "🚀 Starting container with browser control ports..."
CONTAINER_ID=$(docker run -d \
    --name agent-zero-browser-test \
    -p 50001:80 \
    -p 5900:5900 \
    -p 6080:6080 \
    -p 9222:9222 \
    agent-zero-browser-test)

echo "✅ Container started: $CONTAINER_ID"

# Wait for container to be ready
echo ""
echo "⏳ Waiting for container to be ready..."
sleep 10

# Test if container is running
if docker ps | grep -q agent-zero-browser-test; then
    echo "✅ Container is running"
else
    echo "❌ Container failed to start"
    docker logs agent-zero-browser-test
    exit 1
fi

# Test Agent Zero UI
echo ""
echo "🌐 Testing Agent Zero UI..."
if curl -s -o /dev/null -w "%{http_code}" http://localhost:50001 | grep -q "200"; then
    echo "✅ Agent Zero UI is accessible at http://localhost:50001"
else
    echo "❌ Agent Zero UI is not accessible"
    docker logs agent-zero-browser-test | tail -20
fi

# Test browser service inside container
echo ""
echo "🧪 Running browser service tests inside container..."
if docker exec agent-zero-browser-test bash -c "
if [ -f /tests/test_docker_browser_service.sh ]; then
    /tests/test_docker_browser_service.sh
else
    echo 'Test script not found, running basic checks...'
    which Xvfb x11vnc websockify chromium-browser >/dev/null 2>&1 && echo 'Basic tools available'
fi
"; then
    echo "✅ Browser service tests passed"
else
    echo "⚠️  Some browser service tests failed (this may be normal on first run)"
fi

# Instructions for manual testing
echo ""
echo "🎯 Manual Testing Instructions:"
echo "================================"
echo ""
echo "1. Open Agent Zero: http://localhost:50001"
echo "2. Go to Settings → Browser Model"
echo "3. Enable 'Interactive Mode' toggle"
echo "4. Save settings"
echo "5. Start a browser task: 'Navigate to google.com'"
echo "6. Look for 'Take Manual Control' button"
echo "7. Click button to test VNC modal"
echo ""
echo "VNC Access Options:"
echo "- Direct VNC: vnc://localhost:5900 (use VNC client)"
echo "- Web VNC: http://localhost:6080 (browser-based)"
echo "- DevTools: http://localhost:9222 (Chrome debugging)"
echo ""

# Provide cleanup instructions
echo "🧹 To clean up when done testing:"
echo "================================="
echo ""
echo "docker stop agent-zero-browser-test"
echo "docker rm agent-zero-browser-test"
echo "docker rmi agent-zero-browser-test"
echo ""

echo "✅ Docker test setup complete!"
echo ""
echo "The container is running and ready for testing."
echo "Press Ctrl+C when you want to stop monitoring..."

# Keep script running and show logs
trap 'echo ""; echo "🛑 Stopping container..."; docker stop agent-zero-browser-test >/dev/null 2>&1; docker rm agent-zero-browser-test >/dev/null 2>&1; echo "✅ Cleanup complete"; exit 0' INT

echo ""
echo "📊 Container logs (Ctrl+C to exit):"
echo "=================================="
docker logs -f agent-zero-browser-test