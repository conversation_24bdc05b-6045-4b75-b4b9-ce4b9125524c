#!/usr/bin/env python3
"""
Debug script to test browser service startup in Docker
"""

import subprocess
import asyncio
import os
import time

async def test_browser_service_components():
    """Test each component of the browser service individually"""
    
    print("🔍 Testing Browser Service Components")
    print("=" * 50)
    
    # Test 1: Check if required binaries exist
    print("\n1. Checking required binaries...")
    binaries = ['Xvfb', 'x11vnc', 'websockify', 'fluxbox']
    
    for binary in binaries:
        try:
            result = subprocess.run(['which', binary], capture_output=True, text=True)
            if result.returncode == 0:
                print(f"✅ {binary}: {result.stdout.strip()}")
            else:
                print(f"❌ {binary}: Not found")
        except Exception as e:
            print(f"❌ {binary}: Error checking - {e}")
    
    # Test 2: Check Chrome/Chromium
    print("\n2. Checking Chrome/Chromium...")
    chrome_binaries = ['chromium', 'chromium-browser', 'google-chrome', 'google-chrome-stable']
    chrome_found = False
    
    for chrome in chrome_binaries:
        try:
            result = subprocess.run(['which', chrome], capture_output=True, text=True)
            if result.returncode == 0:
                print(f"✅ {chrome}: {result.stdout.strip()}")
                chrome_found = True
                break
        except Exception as e:
            continue
    
    if not chrome_found:
        print("❌ No Chrome/Chromium browser found")
    
    # Test 3: Try starting Xvfb
    print("\n3. Testing Xvfb startup...")
    display = ':99'
    
    try:
        # Kill any existing Xvfb
        subprocess.run(['pkill', 'Xvfb'], capture_output=True)
        await asyncio.sleep(1)
        
        xvfb_cmd = [
            'Xvfb', display,
            '-screen', '0', '1920x1080x24',
            '-ac', '+extension', 'GLX', '+render', '-noreset'
        ]
        
        print(f"Starting: {' '.join(xvfb_cmd)}")
        xvfb_process = subprocess.Popen(
            xvfb_cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE
        )
        
        await asyncio.sleep(2)
        
        if xvfb_process.poll() is None:
            print("✅ Xvfb started successfully")
            
            # Set environment
            os.environ['DISPLAY'] = display
            
            # Test 4: Try starting VNC
            print("\n4. Testing VNC server startup...")
            vnc_cmd = [
                'x11vnc', '-display', display,
                '-nopw', '-forever', '-shared',
                '-rfbport', '5900', '-bg'
            ]
            
            print(f"Starting: {' '.join(vnc_cmd)}")
            vnc_result = subprocess.run(vnc_cmd, capture_output=True, text=True)
            
            if vnc_result.returncode == 0:
                print("✅ VNC server started")
                
                # Test 5: Try starting websockify
                print("\n5. Testing websockify startup...")
                websockify_cmd = [
                    'websockify', '--web', '/usr/share/novnc',
                    '6080', 'localhost:5900'
                ]
                
                print(f"Starting: {' '.join(websockify_cmd)}")
                websockify_process = subprocess.Popen(
                    websockify_cmd,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE
                )
                
                await asyncio.sleep(2)
                
                if websockify_process.poll() is None:
                    print("✅ websockify started")
                    
                    # Test 6: Check port accessibility
                    print("\n6. Testing port accessibility...")
                    ports = [5900, 6080, 9222]
                    
                    for port in ports:
                        try:
                            result = subprocess.run(
                                ['netstat', '-tuln'], 
                                capture_output=True, text=True
                            )
                            if f':{port} ' in result.stdout:
                                print(f"✅ Port {port}: Listening")
                            else:
                                print(f"⚠️  Port {port}: Not listening")
                        except Exception as e:
                            print(f"❌ Port {port}: Error checking - {e}")
                    
                    # Cleanup
                    print("\n7. Cleaning up...")
                    subprocess.run(['pkill', 'websockify'], capture_output=True)
                    websockify_process.terminate()
                else:
                    print("❌ websockify failed to start")
                    print(f"STDOUT: {websockify_process.stdout.read().decode()}")
                    print(f"STDERR: {websockify_process.stderr.read().decode()}")
                
                subprocess.run(['pkill', 'x11vnc'], capture_output=True)
            else:
                print("❌ VNC server failed to start")
                print(f"STDOUT: {vnc_result.stdout}")
                print(f"STDERR: {vnc_result.stderr}")
            
            xvfb_process.terminate()
        else:
            print("❌ Xvfb failed to start")
            print(f"STDOUT: {xvfb_process.stdout.read().decode()}")
            print(f"STDERR: {xvfb_process.stderr.read().decode()}")
            
    except Exception as e:
        print(f"❌ Error testing Xvfb: {e}")
    
    print("\n" + "=" * 50)
    print("Browser service component test completed!")

if __name__ == "__main__":
    asyncio.run(test_browser_service_components())