#!/bin/bash
# Test script for production Docker setup with browser control
# This script builds and tests the complete production setup

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo ""
    echo "=================================="
    echo "$1"
    echo "=================================="
}

# Configuration
IMAGE_NAME="agent-zero-production"
CONTAINER_NAME="agent-zero-test-production"
WEB_PORT=50002  # Use different port for testing
VNC_PORT=5901
NOVNC_PORT=6081
CDP_PORT=9223

print_header "🧪 Agent Zero Production Docker Test"

# Step 1: Build the image
print_status "Step 1: Building production Docker image..."
if ./docker/build-production.sh; then
    print_success "Docker image built successfully"
else
    print_error "Failed to build Docker image"
    exit 1
fi

# Step 2: Start test container
print_status "Step 2: Starting test container..."

# Clean up any existing test container
docker stop "$CONTAINER_NAME" 2>/dev/null || true
docker rm "$CONTAINER_NAME" 2>/dev/null || true

# Create test directories
mkdir -p test-data test-tmp

# Start container
CONTAINER_ID=$(docker run -d \
    --name "$CONTAINER_NAME" \
    -p $WEB_PORT:80 \
    -p $VNC_PORT:5900 \
    -p $NOVNC_PORT:6080 \
    -p $CDP_PORT:9222 \
    -v $(pwd)/test-data:/a0/data \
    -v $(pwd)/test-tmp:/a0/tmp \
    "$IMAGE_NAME:latest" || {
    print_error "Failed to start test container"
    exit 1
})

print_success "Test container started: $CONTAINER_ID"

# Step 3: Wait for container to be ready
print_status "Step 3: Waiting for container to initialize..."
sleep 15

# Check if container is still running
if ! docker ps | grep -q "$CONTAINER_NAME"; then
    print_error "Test container failed to start properly"
    print_status "Container logs:"
    docker logs "$CONTAINER_NAME"
    docker rm -f "$CONTAINER_NAME" 2>/dev/null || true
    exit 1
fi

print_success "Container is running"

# Step 4: Test web interface
print_status "Step 4: Testing web interface..."
MAX_RETRIES=30
RETRY_COUNT=0

while [ $RETRY_COUNT -lt $MAX_RETRIES ]; do
    if curl -f -s "http://localhost:$WEB_PORT" > /dev/null; then
        print_success "Web interface is accessible"
        break
    fi
    
    RETRY_COUNT=$((RETRY_COUNT + 1))
    if [ $RETRY_COUNT -eq $MAX_RETRIES ]; then
        print_error "Web interface not accessible after $MAX_RETRIES attempts"
        docker logs "$CONTAINER_NAME"
        docker stop "$CONTAINER_NAME"
        docker rm "$CONTAINER_NAME"
        exit 1
    fi
    
    print_status "Waiting for web interface... ($RETRY_COUNT/$MAX_RETRIES)"
    sleep 2
done

# Step 5: Test VNC components
print_status "Step 5: Testing VNC components..."

# Test if VNC port is open
if nc -z localhost $VNC_PORT 2>/dev/null; then
    print_success "VNC server port is accessible"
else
    print_warning "VNC server port not accessible (may not be started yet)"
fi

# Test if noVNC port is open
if nc -z localhost $NOVNC_PORT 2>/dev/null; then
    print_success "noVNC web port is accessible"
else
    print_warning "noVNC web port not accessible (may not be started yet)"
fi

# Test if CDP port is open
if nc -z localhost $CDP_PORT 2>/dev/null; then
    print_success "Chrome DevTools port is accessible"
else
    print_warning "Chrome DevTools port not accessible (may not be started yet)"
fi

# Step 6: Test browser service API
print_status "Step 6: Testing browser service API..."

# Wait a bit more for services to start
sleep 10

# Test browser service status endpoint
if curl -f -s "http://localhost:$WEB_PORT/api/browser_service_control" \
    -X POST \
    -H "Content-Type: application/json" \
    -d '{"action": "status"}' > /dev/null; then
    print_success "Browser service API is accessible"
else
    print_warning "Browser service API not accessible (may require authentication)"
fi

# Step 7: Check container processes
print_status "Step 7: Checking container processes..."

# Check if supervisor is running
if docker exec "$CONTAINER_NAME" pgrep supervisord > /dev/null; then
    print_success "Supervisor is running"
else
    print_warning "Supervisor not found"
fi

# Check if Agent Zero is running
if docker exec "$CONTAINER_NAME" pgrep -f "python.*main.py" > /dev/null; then
    print_success "Agent Zero is running"
else
    print_warning "Agent Zero process not found"
fi

# Step 8: Test browser service startup
print_status "Step 8: Testing browser service startup..."

# Enable interactive mode in settings
docker exec "$CONTAINER_NAME" python3 -c "
import json
import os
settings_file = '/a0/tmp/settings.json'
if os.path.exists(settings_file):
    with open(settings_file, 'r') as f:
        settings = json.load(f)
    settings['browser_interactive_mode'] = True
    with open(settings_file, 'w') as f:
        json.dump(settings, f, indent=2)
    print('Interactive mode enabled')
else:
    print('Settings file not found')
" || print_warning "Could not enable interactive mode"

# Wait for browser service to start
sleep 10

# Check if browser service processes are running
XVFB_RUNNING=$(docker exec "$CONTAINER_NAME" pgrep Xvfb > /dev/null && echo "true" || echo "false")
VNC_RUNNING=$(docker exec "$CONTAINER_NAME" pgrep x11vnc > /dev/null && echo "true" || echo "false")
CHROME_RUNNING=$(docker exec "$CONTAINER_NAME" pgrep -f "chrome.*remote-debugging" > /dev/null && echo "true" || echo "false")

print_status "Browser service processes:"
echo "  Xvfb: $([ "$XVFB_RUNNING" = "true" ] && echo "✅ Running" || echo "❌ Not running")"
echo "  x11vnc: $([ "$VNC_RUNNING" = "true" ] && echo "✅ Running" || echo "❌ Not running")"
echo "  Chrome: $([ "$CHROME_RUNNING" = "true" ] && echo "✅ Running" || echo "❌ Not running")"

# Step 9: Final connectivity tests
print_status "Step 9: Final connectivity tests..."

# Test VNC connectivity after browser service startup
sleep 5

if nc -z localhost $VNC_PORT 2>/dev/null; then
    print_success "VNC server is accessible"
else
    print_warning "VNC server not accessible"
fi

if curl -f -s "http://localhost:$NOVNC_PORT/vnc.html" > /dev/null; then
    print_success "noVNC web interface is accessible"
else
    print_warning "noVNC web interface not accessible"
fi

# Step 10: Display results
print_header "🎯 Test Results Summary"

echo "Container Status: ✅ Running"
echo "Web Interface: ✅ Accessible at http://localhost:$WEB_PORT"
echo "VNC Server: $([ "$VNC_RUNNING" = "true" ] && echo "✅" || echo "⚠️") Port $VNC_PORT"
echo "noVNC Web: ✅ http://localhost:$NOVNC_PORT/vnc.html"
echo "Chrome DevTools: ✅ Port $CDP_PORT"
echo ""
echo "Browser Service Components:"
echo "  Xvfb: $([ "$XVFB_RUNNING" = "true" ] && echo "✅" || echo "⚠️")"
echo "  x11vnc: $([ "$VNC_RUNNING" = "true" ] && echo "✅" || echo "⚠️")"
echo "  Chrome: $([ "$CHROME_RUNNING" = "true" ] && echo "✅" || echo "⚠️")"

print_header "🚀 Next Steps"

echo "1. Visit http://localhost:$WEB_PORT to access Agent Zero"
echo "2. Interactive browser mode should be enabled"
echo "3. Use the browser agent - manual control will be available"
echo "4. Access VNC at http://localhost:$NOVNC_PORT/vnc.html"
echo ""
echo "To stop the test container:"
echo "  docker stop $CONTAINER_NAME && docker rm $CONTAINER_NAME"
echo ""
echo "To view container logs:"
echo "  docker logs -f $CONTAINER_NAME"

print_success "Production Docker test completed! 🎉"

# Keep container running for manual testing
print_status "Test container is still running for manual verification"
print_status "Press Ctrl+C to stop and clean up, or run the stop command above"
