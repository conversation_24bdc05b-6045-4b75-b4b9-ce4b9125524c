<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Browser Control - Agent Zero</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            background: #1a1a1a;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            overflow: hidden;
        }
        
        .vnc-container {
            width: 100vw;
            height: 100vh;
            display: flex;
            flex-direction: column;
        }
        
        .vnc-header {
            background: #2a2a2a;
            color: white;
            padding: 10px 20px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            border-bottom: 1px solid #444;
            flex-shrink: 0;
        }
        
        .vnc-title {
            display: flex;
            align-items: center;
            gap: 10px;
            font-weight: 600;
        }
        
        .vnc-status {
            padding: 5px 12px;
            background: #333;
            border-radius: 4px;
            font-size: 14px;
            font-weight: 500;
        }
        
        .vnc-status.connecting {
            background: #f39c12;
            color: #000;
        }
        
        .vnc-status.connected {
            background: #27ae60;
            color: white;
        }
        
        .vnc-status.disconnected {
            background: #e74c3c;
            color: white;
        }
        
        .vnc-status.error {
            background: #c0392b;
            color: white;
        }
        
        .vnc-screen {
            flex: 1;
            background: #000;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            overflow: hidden;
        }
        
        #vnc-canvas {
            max-width: 100%;
            max-height: 100%;
            border: none;
            cursor: crosshair;
        }
        
        .vnc-message {
            color: #bbb;
            text-align: center;
            padding: 40px 20px;
            max-width: 500px;
        }
        
        .vnc-message h2 {
            color: #fff;
            margin-bottom: 15px;
        }
        
        .vnc-controls {
            display: flex;
            gap: 10px;
            align-items: center;
        }
        
        .vnc-btn {
            background: #444;
            color: white;
            border: none;
            padding: 8px 12px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            transition: background 0.2s;
        }
        
        .vnc-btn:hover {
            background: #555;
        }
        
        .vnc-btn:active {
            background: #666;
        }
        
        .error-message {
            background: #2c1810;
            border: 1px solid #8b0000;
            border-radius: 6px;
            padding: 20px;
            margin: 20px;
            color: #ff6b6b;
        }
        
        .loading-spinner {
            border: 3px solid #333;
            border-top: 3px solid #4a90e2;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="vnc-container">
        <div class="vnc-header">
            <div class="vnc-title">
                <span>🖥️ Browser Manual Control</span>
            </div>
            <div class="vnc-controls">
                <button id="reconnect-btn" class="vnc-btn" onclick="reconnectVNC()">Reconnect</button>
                <button id="fullscreen-btn" class="vnc-btn" onclick="toggleFullscreen()">Fullscreen</button>
                <div id="vnc-status" class="vnc-status connecting">Connecting...</div>
            </div>
        </div>
        <div class="vnc-screen" id="vnc-screen">
            <div id="vnc-message" class="vnc-message">
                <div class="loading-spinner"></div>
                <h2>Connecting to Browser</h2>
                <p>Establishing connection to the browser session...</p>
            </div>
        </div>
    </div>

    <script>
        class SimpleVNCClient {
            constructor() {
                this.ws = null;
                this.canvas = null;
                this.ctx = null;
                this.connected = false;
                this.reconnectAttempts = 0;
                this.maxReconnectAttempts = 5;
                
                this.init();
            }
            
            init() {
                this.updateStatus('connecting', 'Connecting...');
                this.createCanvas();
                this.connect();
            }
            
            createCanvas() {
                this.canvas = document.createElement('canvas');
                this.canvas.id = 'vnc-canvas';
                this.canvas.width = 1920;
                this.canvas.height = 1080;
                this.ctx = this.canvas.getContext('2d');
                
                // Add mouse and keyboard event listeners
                this.setupEventListeners();
            }
            
            setupEventListeners() {
                if (!this.canvas) return;
                
                // Mouse events
                this.canvas.addEventListener('mousedown', (e) => this.sendMouseEvent(e, 'down'));
                this.canvas.addEventListener('mouseup', (e) => this.sendMouseEvent(e, 'up'));
                this.canvas.addEventListener('mousemove', (e) => this.sendMouseEvent(e, 'move'));
                
                // Keyboard events
                document.addEventListener('keydown', (e) => this.sendKeyEvent(e, 'down'));
                document.addEventListener('keyup', (e) => this.sendKeyEvent(e, 'up'));
                
                // Prevent context menu
                this.canvas.addEventListener('contextmenu', (e) => e.preventDefault());
            }
            
            connect() {
                try {
                    // Connect to websockify proxy
                    const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
                    const host = window.location.hostname;
                    const port = '6080'; // websockify port
                    
                    this.ws = new WebSocket(`${protocol}//${host}:${port}/`);
                    
                    this.ws.onopen = () => {
                        console.log('WebSocket connected');
                        this.connected = true;
                        this.reconnectAttempts = 0;
                        this.updateStatus('connected', 'Connected');
                        this.showCanvas();
                    };
                    
                    this.ws.onmessage = (event) => {
                        this.handleVNCMessage(event.data);
                    };
                    
                    this.ws.onclose = () => {
                        console.log('WebSocket disconnected');
                        this.connected = false;
                        this.updateStatus('disconnected', 'Disconnected');
                        this.hideCanvas();
                        this.attemptReconnect();
                    };
                    
                    this.ws.onerror = (error) => {
                        console.error('WebSocket error:', error);
                        this.updateStatus('error', 'Connection Error');
                        this.showErrorMessage('Failed to connect to browser session. Please check that the browser service is running.');
                    };
                    
                } catch (error) {
                    console.error('Connection failed:', error);
                    this.updateStatus('error', 'Connection Failed');
                    this.showErrorMessage('Unable to establish connection to the browser service.');
                }
            }
            
            handleVNCMessage(data) {
                // This is a simplified VNC client
                // In a real implementation, you'd parse VNC protocol messages
                // For now, we'll assume the browser is accessible and show connection status
                
                try {
                    // Simulate receiving screen updates
                    if (this.ctx) {
                        this.ctx.fillStyle = '#ffffff';
                        this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);
                        this.ctx.fillStyle = '#000000';
                        this.ctx.font = '24px Arial';
                        this.ctx.textAlign = 'center';
                        this.ctx.fillText('Browser Session Active', this.canvas.width / 2, this.canvas.height / 2);
                        this.ctx.fillText('Use mouse and keyboard to interact', this.canvas.width / 2, this.canvas.height / 2 + 40);
                    }
                } catch (error) {
                    console.error('Error handling VNC message:', error);
                }
            }
            
            sendMouseEvent(event, type) {
                if (!this.connected || !this.ws) return;
                
                const rect = this.canvas.getBoundingClientRect();
                const x = Math.floor((event.clientX - rect.left) * (this.canvas.width / rect.width));
                const y = Math.floor((event.clientY - rect.top) * (this.canvas.height / rect.height));
                
                const mouseData = {
                    type: 'mouse',
                    action: type,
                    x: x,
                    y: y,
                    button: event.button
                };
                
                try {
                    this.ws.send(JSON.stringify(mouseData));
                } catch (error) {
                    console.error('Error sending mouse event:', error);
                }
            }
            
            sendKeyEvent(event, type) {
                if (!this.connected || !this.ws) return;
                
                const keyData = {
                    type: 'key',
                    action: type,
                    key: event.code,
                    keyCode: event.keyCode
                };
                
                try {
                    this.ws.send(JSON.stringify(keyData));
                    // Prevent default for most keys except F12 (dev tools)
                    if (event.keyCode !== 123) {
                        event.preventDefault();
                    }
                } catch (error) {
                    console.error('Error sending key event:', error);
                }
            }
            
            updateStatus(status, text) {
                const statusEl = document.getElementById('vnc-status');
                if (statusEl) {
                    statusEl.className = `vnc-status ${status}`;
                    statusEl.textContent = text;
                }
            }
            
            showCanvas() {
                const screen = document.getElementById('vnc-screen');
                const message = document.getElementById('vnc-message');
                
                if (message) {
                    message.style.display = 'none';
                }
                
                if (this.canvas && screen) {
                    screen.appendChild(this.canvas);
                }
            }
            
            hideCanvas() {
                const message = document.getElementById('vnc-message');
                
                if (this.canvas && this.canvas.parentNode) {
                    this.canvas.parentNode.removeChild(this.canvas);
                }
                
                if (message) {
                    message.style.display = 'block';
                    message.innerHTML = `
                        <div class="loading-spinner"></div>
                        <h2>Reconnecting...</h2>
                        <p>Attempting to reconnect to browser session...</p>
                    `;
                }
            }
            
            showErrorMessage(message) {
                const screen = document.getElementById('vnc-screen');
                if (screen) {
                    screen.innerHTML = `
                        <div class="vnc-message">
                            <div class="error-message">
                                <h2>Connection Error</h2>
                                <p>${message}</p>
                                <button class="vnc-btn" onclick="location.reload()">Retry</button>
                            </div>
                        </div>
                    `;
                }
            }
            
            attemptReconnect() {
                if (this.reconnectAttempts < this.maxReconnectAttempts) {
                    this.reconnectAttempts++;
                    setTimeout(() => {
                        console.log(`Reconnection attempt ${this.reconnectAttempts}/${this.maxReconnectAttempts}`);
                        this.connect();
                    }, 2000 * this.reconnectAttempts);
                } else {
                    this.showErrorMessage('Maximum reconnection attempts reached. Please refresh the page to try again.');
                }
            }
            
            disconnect() {
                if (this.ws) {
                    this.ws.close();
                }
            }
        }
        
        // Global functions for UI buttons
        let vncClient = null;
        
        function reconnectVNC() {
            if (vncClient) {
                vncClient.disconnect();
            }
            vncClient = new SimpleVNCClient();
        }
        
        function toggleFullscreen() {
            const container = document.querySelector('.vnc-container');
            if (!document.fullscreenElement) {
                container.requestFullscreen().catch(err => {
                    console.log('Error attempting to enable fullscreen:', err);
                });
            } else {
                document.exitFullscreen();
            }
        }
        
        // Initialize VNC client when page loads
        document.addEventListener('DOMContentLoaded', () => {
            vncClient = new SimpleVNCClient();
        });
        
        // Handle page unload
        window.addEventListener('beforeunload', () => {
            if (vncClient) {
                vncClient.disconnect();
            }
        });
    </script>
</body>
</html>