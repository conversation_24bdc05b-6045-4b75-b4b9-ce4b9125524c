/**
 * Browser Control Modal - Provides VNC access to manual browser control
 */

let browserControlModal = null;
let browserControlTimer = null;
let browserControlStartTime = null;
let currentSessionId = null;

/**
 * Open the browser control modal with VNC interface
 * @param {string} sessionId - The browser session ID
 * @param {string} vncUrl - The VNC URL (optional, will construct if not provided)
 */
function openBrowserControl(sessionId, vncUrl) {
  // Store current session
  currentSessionId = sessionId;
  
  // Get CSRF token from Alpine store if available
  const csrfToken = Alpine.store('appState')?.csrfToken || '';
  
  // Construct VNC URL if not provided
  if (!vncUrl) {
    vncUrl = `http://localhost:6080/vnc.html`;
  }
  
  // Create modal content
  const modalContent = `
    <div class="browser-control-container">
      <div class="browser-control-header">
        <div class="control-info">
          <span class="material-symbols-outlined">touch_app</span>
          <span>Manual Browser Control Active</span>
          <span id="control-timer" class="control-timer">00:00</span>
        </div>
        <div class="control-actions">
          <button onclick="refreshBrowserControl()" class="control-action-btn" title="Refresh">
            <span class="material-symbols-outlined">refresh</span>
          </button>
          <button onclick="releaseBrowserControl()" class="control-action-btn release-btn" title="Release Control">
            <span class="material-symbols-outlined">close</span>
            Release Control
          </button>
        </div>
      </div>
      <div class="browser-control-body">
        <iframe 
          id="vnc-frame"
          src="${vncUrl}?token=${csrfToken}"
          style="width: 100%; height: 100%; border: none;"
          allow="clipboard-read; clipboard-write">
        </iframe>
      </div>
      <div class="browser-control-footer">
        <div class="control-help">
          <span class="material-symbols-outlined">info</span>
          <span>Use mouse and keyboard to interact with the browser. Click "Release Control" when done.</span>
        </div>
      </div>
    </div>
  `;
  
  // Create modal using the existing modal system
  browserControlModal = genericModalProxy.openModal(
    'Browser Manual Control',
    'Use mouse and keyboard to interact with the browser. Click "Release Control" when done.',
    modalContent
  );
  
  // Start timer
  startControlTimer();
  
  // Setup resize handling
  setupModalResize();
  
  // Notify backend that control has been taken
  takeControlNotification(sessionId);
}

/**
 * Start the control timer
 */
function startControlTimer() {
  browserControlStartTime = Date.now();
  
  // Update timer every second
  browserControlTimer = setInterval(() => {
    const elapsed = Math.floor((Date.now() - browserControlStartTime) / 1000);
    const minutes = Math.floor(elapsed / 60);
    const seconds = elapsed % 60;
    
    const timerElement = document.getElementById('control-timer');
    if (timerElement) {
      timerElement.textContent = `${String(minutes).padStart(2, '0')}:${String(seconds).padStart(2, '0')}`;
      
      // Warn after 25 minutes (timeout at 30)
      if (elapsed > 1500) {
        timerElement.classList.add('timer-warning');
      }
    } else {
      // Modal closed, stop timer
      clearInterval(browserControlTimer);
    }
  }, 1000);
}

/**
 * Release browser control and close modal
 */
function releaseBrowserControl() {
  // Clear timer
  if (browserControlTimer) {
    clearInterval(browserControlTimer);
    browserControlTimer = null;
  }
  
  // Close modal
  if (browserControlModal) {
    genericModalProxy.handleClose();
    browserControlModal = null;
  }
  
  // Notify backend that control has been released
  if (currentSessionId) {
    releaseControlNotification(currentSessionId);
    currentSessionId = null;
  }
}

/**
 * Setup modal resize functionality
 */
function setupModalResize() {
  // Wait for modal to be rendered
  setTimeout(() => {
    const modalContainer = document.querySelector('.modal-overlay .modal-container');
    if (modalContainer) {
      // Make modal resizable by adding event listeners
      makeModalResizable(modalContainer);
      
      // Handle window resize
      window.addEventListener('resize', handleWindowResize);
      
      // Adjust VNC frame on resize
      const resizeObserver = new ResizeObserver(handleModalResize);
      resizeObserver.observe(modalContainer);
    }
  }, 100);
}

/**
 * Make modal resizable
 */
function makeModalResizable(modalContainer) {
  modalContainer.style.resize = 'both';
  modalContainer.style.overflow = 'hidden';
  
  // Add minimum size constraints
  modalContainer.style.minWidth = '800px';
  modalContainer.style.minHeight = '600px';
  
  // For mobile devices, allow different minimum sizes
  if (window.innerWidth <= 768) {
    modalContainer.style.minWidth = '300px';
    modalContainer.style.minHeight = '400px';
  }
}

/**
 * Handle modal resize events
 */
function handleModalResize(entries) {
  for (const entry of entries) {
    const iframe = document.getElementById('vnc-frame');
    if (iframe) {
      // Force iframe to recalculate its size
      iframe.style.width = '100%';
      iframe.style.height = '100%';
      
      // Trigger resize event in noVNC client if it exists
      try {
        iframe.contentWindow?.postMessage({type: 'resize'}, '*');
      } catch (e) {
        // Ignore cross-origin errors
      }
    }
  }
}

/**
 * Handle window resize
 */
function handleWindowResize() {
  const modalContainer = document.querySelector('.modal-overlay .modal-container');
  if (modalContainer) {
    // Ensure modal doesn't exceed viewport
    const maxWidth = window.innerWidth * 0.95;
    const maxHeight = window.innerHeight * 0.95;
    
    if (modalContainer.offsetWidth > maxWidth) {
      modalContainer.style.width = maxWidth + 'px';
    }
    if (modalContainer.offsetHeight > maxHeight) {
      modalContainer.style.height = maxHeight + 'px';
    }
    
    // Trigger modal resize handler
    handleModalResize([{target: modalContainer}]);
  }
}

/**
 * Refresh the VNC iframe
 */
function refreshBrowserControl() {
  const iframe = document.getElementById('vnc-frame');
  if (iframe) {
    const currentSrc = iframe.src;
    iframe.src = '';
    setTimeout(() => {
      iframe.src = currentSrc;
    }, 100);
  }
}

/**
 * Notify backend that control has been taken
 */
async function takeControlNotification(sessionId) {
  try {
    const response = await fetch('/browser_control_take', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-CSRF-Token': Alpine.store('appState')?.csrfToken || ''
      },
      body: JSON.stringify({ session_id: sessionId })
    });

    if (!response.ok) {
      console.error('Failed to notify control taken:', response.statusText);
    }
  } catch (error) {
    console.error('Error notifying control taken:', error);
  }
}

/**
 * Notify backend that control has been released
 */
async function releaseControlNotification(sessionId) {
  try {
    const response = await fetch('/browser_control_release', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-CSRF-Token': Alpine.store('appState')?.csrfToken || ''
      },
      body: JSON.stringify({ session_id: sessionId })
    });

    if (!response.ok) {
      console.error('Failed to notify control released:', response.statusText);
    }
  } catch (error) {
    console.error('Error notifying control released:', error);
  }
}

/**
 * Handle browser control button click - check for active sessions first
 */
async function handleBrowserControlClick() {
  try {
    // Check if there are any active browser sessions
    const response = await fetch('/browser_sessions', {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'X-CSRF-Token': Alpine.store('appState')?.csrfToken || ''
      }
    });

    if (!response.ok) {
      throw new Error(`Failed to get browser sessions: ${response.statusText}`);
    }

    const data = await response.json();
    const sessions = data.sessions || [];

    if (sessions.length === 0) {
      // No active sessions, try to start browser service
      try {
        const startResponse = await fetch('/browser_start_service', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'X-CSRF-Token': Alpine.store('appState')?.csrfToken || ''
          }
        });

        if (startResponse.ok) {
          const serviceInfo = await startResponse.json();
          if (serviceInfo.status === 'success') {
            openBrowserControl('manual-session', serviceInfo.novnc_url);
          } else {
            alert('Failed to start browser service: ' + serviceInfo.message);
          }
        } else {
          alert('Failed to start browser service. Interactive mode may not be available on this system.');
        }
      } catch (error) {
        console.error('Error starting browser service:', error);
        alert('Failed to start browser service: ' + error.message);
      }
      return;
    }

    // Check if we have sessions but none with VNC
    const sessionsWithVnc = sessions.filter(session => session.vnc_url);
    const headlessSessions = sessions.filter(session => session.session_active && !session.vnc_url);

    if (sessionsWithVnc.length === 0 && headlessSessions.length > 0) {
      // We have browser sessions but they're all headless
      let message = `Found ${headlessSessions.length} active browser session(s), but they are running in headless mode.\n\n`;

      if (headlessSessions.some(s => s.interactive_fallback)) {
        message += "Interactive mode was attempted but failed. This usually happens on macOS when not running in Docker.\n\n";
      }

      message += "Options:\n";
      message += "1. Use the browser agent in headless mode (no manual control)\n";
      message += "2. Enable Docker mode for interactive browser control\n";
      message += "3. Try starting the browser service manually";

      const userChoice = confirm(message + "\n\nWould you like to try starting the browser service anyway?");

      if (userChoice) {
        // Try to start browser service
        try {
          const startResponse = await fetch('/browser_start_service', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'X-CSRF-Token': Alpine.store('appState')?.csrfToken || ''
            }
          });

          if (startResponse.ok) {
            const serviceInfo = await startResponse.json();
            if (serviceInfo.status === 'success') {
              openBrowserControl('manual-session', serviceInfo.novnc_url);
            } else {
              alert('Failed to start browser service: ' + serviceInfo.message);
            }
          } else {
            alert('Failed to start browser service. Interactive mode may not be available on this system.');
          }
        } catch (error) {
          console.error('Error starting browser service:', error);
          alert('Failed to start browser service: ' + error.message);
        }
      }
      return;
    }

    // Use the first session with VNC available
    if (sessionsWithVnc.length > 0) {
      const sessionWithVnc = sessionsWithVnc[0];
      openBrowserControl(sessionWithVnc.session_id, sessionWithVnc.vnc_url);
    } else {
      // This shouldn't happen as we handled the no-VNC case above, but just in case
      alert('No VNC-enabled browser sessions found.');
    }

  } catch (error) {
    console.error('Error checking browser sessions:', error);
    // Fallback to default VNC URL
    openBrowserControl('fallback-session', 'http://localhost:6080/vnc.html');
  }
}

// Export functions for use in other modules
window.openBrowserControl = openBrowserControl;
window.releaseBrowserControl = releaseBrowserControl;
window.refreshBrowserControl = refreshBrowserControl;
window.handleBrowserControlClick = handleBrowserControlClick;