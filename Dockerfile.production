# Production Dockerfile for Agent Zero with Browser Control
FROM agent0ai/agent-zero:development

# Set environment variables
ENV DEBIAN_FRONTEND=noninteractive
ENV DISPLAY=:99

# Install X11 and VNC components for browser control
RUN apt-get update && apt-get install -y \
    xvfb \
    x11vnc \
    websockify \
    fluxbox \
    novnc \
    chromium \
    && rm -rf /var/lib/apt/lists/*

# Copy our enhanced files
COPY python/ /a0/python/
COPY webui/ /a0/webui/
COPY docker/run/fs/ /

# Make scripts executable
RUN chmod +x /exe/*.sh

# Create necessary directories
RUN mkdir -p /a0/data /a0/tmp

# Copy current files to /git/agent-zero for production build
COPY . /git/agent-zero/

# Expose ports for browser control
EXPOSE 80 5900 6080 9222

# Use the initialization script to properly start all services
CMD ["/exe/initialize.sh", "local"]
