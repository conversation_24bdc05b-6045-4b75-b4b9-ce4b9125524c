{"version": "1.0", "chat_model_provider": "openai", "chat_model_name": "gpt-4o-2024-08-06", "chat_model_api_base": "", "chat_model_kwargs": {}, "chat_model_ctx_length": 128000, "chat_model_ctx_history": 0.8, "chat_model_vision": true, "chat_model_rl_requests": 100, "chat_model_rl_input": 50000, "chat_model_rl_output": 10000, "util_model_provider": "openai", "util_model_name": "gpt-4o-mini", "util_model_api_base": "", "util_model_kwargs": {}, "util_model_ctx_length": 128000, "util_model_ctx_input": 0.8, "util_model_rl_requests": 100, "util_model_rl_input": 50000, "util_model_rl_output": 10000, "embed_model_provider": "openai", "embed_model_name": "text-embedding-3-small", "embed_model_api_base": "", "embed_model_kwargs": {}, "embed_model_rl_requests": 100, "embed_model_rl_input": 50000, "browser_model_provider": "openai", "browser_model_name": "gpt-4o-2024-08-06", "browser_model_api_base": "", "browser_model_vision": true, "browser_model_rl_requests": 100, "browser_model_rl_input": 50000, "browser_model_rl_output": 10000, "browser_model_kwargs": {}, "browser_interactive_mode": true, "browser_vnc_port": 5900, "browser_novnc_port": 6080, "browser_cdp_port": 9222, "agent_profile": "default", "agent_memory_subdir": "agent_memory", "agent_knowledge_subdir": "agent_knowledge", "memory_recall_enabled": true, "memory_recall_delayed": true, "memory_recall_interval": 5, "memory_recall_history_len": 10, "memory_recall_memories_max_search": 10, "memory_recall_solutions_max_search": 5, "memory_recall_memories_max_result": 5, "memory_recall_solutions_max_result": 3, "memory_recall_similarity_threshold": 0.8, "memory_recall_query_prep": true, "memory_recall_post_filter": true, "memory_memorize_enabled": true, "memory_memorize_consolidation": true, "memory_memorize_replace_threshold": 0.85, "api_keys": {"openai": "YOUR_OPENAI_API_KEY_HERE"}, "auth_login": "", "auth_password": "", "root_password": "", "rfc_auto_docker": false, "rfc_url": "", "rfc_password": "", "rfc_port_http": 0, "rfc_port_ssh": 0, "shell_interface": "local", "stt_model_size": "base", "stt_language": "auto", "stt_silence_threshold": 0.5, "stt_silence_duration": 2, "stt_waiting_timeout": 10, "tts_kokoro": false, "mcp_servers": "", "mcp_client_init_timeout": 30, "mcp_client_tool_timeout": 60, "mcp_server_enabled": false, "mcp_server_token": "", "a2a_server_enabled": false, "secrets": ""}