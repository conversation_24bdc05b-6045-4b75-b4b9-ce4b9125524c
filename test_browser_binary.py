#!/usr/bin/env python3
"""
Test script to check if browser binary can be found for the browser service
"""

import os
import subprocess
from python.helpers.browser_service import BrowserService

def test_browser_binary():
    print("Testing browser binary detection...")
    
    # Create a browser service instance
    service = BrowserService()
    
    # Try to find the browser binary
    binary_path = service._find_chrome_binary()
    
    if binary_path:
        print(f"✅ Browser binary found: {binary_path}")
        
        # Check if the binary is executable
        if os.path.exists(binary_path) and os.access(binary_path, os.X_OK):
            print(f"✅ Binary is executable")
            
            # Try to get version to verify it works
            try:
                result = subprocess.run(
                    [binary_path, '--version'],
                    capture_output=True,
                    text=True,
                    timeout=10
                )
                if result.returncode == 0:
                    print(f"✅ Browser version: {result.stdout.strip()}")
                else:
                    print(f"❌ Browser version check failed: {result.stderr}")
            except Exception as e:
                print(f"❌ Error checking browser version: {e}")
        else:
            print(f"❌ Binary is not executable or doesn't exist")
    else:
        print("❌ No browser binary found")
        
        # List common browser paths and check if they exist
        print("\nChecking common browser paths:")
        common_paths = [
            '/usr/bin/chromium',
            '/usr/bin/chromium-browser',
            '/usr/bin/google-chrome-stable',
            '/usr/bin/google-chrome',
        ]
        
        for path in common_paths:
            exists = os.path.exists(path)
            print(f"  {path}: {'✅' if exists else '❌'}")

if __name__ == "__main__":
    test_browser_binary()