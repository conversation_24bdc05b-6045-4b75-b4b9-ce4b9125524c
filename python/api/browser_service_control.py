"""
Browser Service Control API - Start/stop browser service dynamically
"""

from python.helpers.api import <PERSON><PERSON><PERSON><PERSON><PERSON>, Request, Response
from python.helpers.browser_service import get_browser_service
from python.helpers import settings
import subprocess
import asyncio


class BrowserServiceControl(ApiHandler):
    """Control browser service startup and shutdown"""
    
    @classmethod
    def requires_auth(cls) -> bool:
        return True
    
    @classmethod
    def requires_csrf(cls) -> bool:
        return True
    
    @classmethod
    def get_methods(cls) -> list[str]:
        return ["POST"]
    
    async def process(self, input: dict, request: Request) -> dict | Response:
        """
        Control browser service based on interactive mode setting
        """
        try:
            action = input.get('action', 'status')
            
            if action == 'status':
                return await self._get_status()
            elif action == 'start':
                return await self._start_service()
            elif action == 'stop':
                return await self._stop_service()
            elif action == 'restart':
                return await self._restart_service()
            else:
                return {
                    "status": "error",
                    "message": f"Unknown action: {action}"
                }
                
        except Exception as e:
            return {
                "status": "error",
                "message": f"Browser service control failed: {str(e)}"
            }
    
    async def _get_status(self) -> dict:
        """Get current browser service status"""
        try:
            browser_service = await get_browser_service()
            
            # Check if processes are running
            processes_status = {}
            
            # Check Xvfb
            try:
                result = subprocess.run(['pgrep', 'Xvfb'], capture_output=True)
                processes_status['xvfb'] = result.returncode == 0
            except:
                processes_status['xvfb'] = False
            
            # Check x11vnc
            try:
                result = subprocess.run(['pgrep', 'x11vnc'], capture_output=True)
                processes_status['vnc'] = result.returncode == 0
            except:
                processes_status['vnc'] = False
            
            # Check websockify
            try:
                result = subprocess.run(['pgrep', '-f', 'websockify'], capture_output=True)
                processes_status['websockify'] = result.returncode == 0
            except:
                processes_status['websockify'] = False
            
            # Check Chrome
            try:
                result = subprocess.run(['pgrep', '-f', 'chrome.*remote-debugging'], capture_output=True)
                processes_status['chrome'] = result.returncode == 0
            except:
                processes_status['chrome'] = False
            
            is_healthy = browser_service.is_healthy() if browser_service else False
            all_processes_running = all(processes_status.values())
            
            # Get current settings
            current_settings = settings.get_settings()
            interactive_mode = current_settings.get("browser_interactive_mode", False)
            
            return {
                "status": "success",
                "browser_service": {
                    "is_healthy": is_healthy,
                    "is_running": browser_service.is_running if browser_service else False,
                    "processes": processes_status,
                    "all_processes_running": all_processes_running,
                    "interactive_mode_enabled": interactive_mode,
                    "connection_info": browser_service.get_connection_info() if browser_service and browser_service.is_running else None
                }
            }
            
        except Exception as e:
            return {
                "status": "error",
                "message": f"Failed to get browser service status: {str(e)}"
            }
    
    async def _start_service(self) -> dict:
        """Start browser service"""
        try:
            # Check if interactive mode is enabled
            current_settings = settings.get_settings()
            if not current_settings.get("browser_interactive_mode", False):
                return {
                    "status": "error",
                    "message": "Interactive mode is not enabled in settings"
                }
            
            browser_service = await get_browser_service()
            
            if browser_service.is_healthy():
                return {
                    "status": "success",
                    "message": "Browser service is already running",
                    "connection_info": browser_service.get_connection_info()
                }
            
            # Start the service
            connection_info = await browser_service.start()
            
            return {
                "status": "success",
                "message": "Browser service started successfully",
                "connection_info": connection_info
            }
            
        except Exception as e:
            return {
                "status": "error",
                "message": f"Failed to start browser service: {str(e)}"
            }
    
    async def _stop_service(self) -> dict:
        """Stop browser service"""
        try:
            browser_service = await get_browser_service()
            
            if not browser_service.is_running:
                return {
                    "status": "success",
                    "message": "Browser service is already stopped"
                }
            
            await browser_service.stop()
            
            return {
                "status": "success",
                "message": "Browser service stopped successfully"
            }
            
        except Exception as e:
            return {
                "status": "error",
                "message": f"Failed to stop browser service: {str(e)}"
            }
    
    async def _restart_service(self) -> dict:
        """Restart browser service"""
        try:
            browser_service = await get_browser_service()
            await browser_service.restart()
            
            connection_info = browser_service.get_connection_info()
            
            return {
                "status": "success",
                "message": "Browser service restarted successfully",
                "connection_info": connection_info
            }
            
        except Exception as e:
            return {
                "status": "error",
                "message": f"Failed to restart browser service: {str(e)}"
            }
