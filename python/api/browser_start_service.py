"""
Browser Start Service API - Start the browser service for manual control
"""

from python.helpers.api import <PERSON><PERSON><PERSON><PERSON><PERSON>, Request, Response
from python.helpers.browser_service import get_browser_service
from python.helpers import settings


class BrowserStartService(ApiHandler):
    """Start browser service for manual control"""
    
    @classmethod
    def requires_auth(cls) -> bool:
        return True
    
    @classmethod
    def requires_csrf(cls) -> bool:
        return True
    
    @classmethod
    def get_methods(cls) -> list[str]:
        return ["POST"]
    
    async def process(self, input: dict, request: Request) -> dict | Response:
        """
        Start the browser service if not already running
        """
        try:
            # Check if interactive mode is enabled
            current_settings = settings.get_settings()
            if not current_settings.get("browser_interactive_mode", False):
                return {
                    "status": "error",
                    "message": "Interactive mode is not enabled in settings. Please enable it in Settings > Browser Model."
                }
            
            # Get browser service configuration
            browser_config = {
                "vnc_port": current_settings.get("browser_vnc_port", 5900),
                "novnc_port": current_settings.get("browser_novnc_port", 6080),
                "cdp_port": current_settings.get("browser_cdp_port", 9222),
            }
            
            browser_service = await get_browser_service(browser_config)
            
            # Check if already running
            if browser_service.is_healthy():
                connection_info = browser_service.get_connection_info()
                return {
                    "status": "success",
                    "message": "Browser service is already running",
                    "novnc_url": connection_info.get("novnc_url"),
                    "vnc_url": connection_info.get("vnc_url"),
                    "cdp_url": connection_info.get("cdp_url")
                }
            
            # Start the service
            connection_info = await browser_service.start()
            
            return {
                "status": "success",
                "message": "Browser service started successfully",
                "novnc_url": connection_info.get("novnc_url"),
                "vnc_url": connection_info.get("vnc_url"),
                "cdp_url": connection_info.get("cdp_url")
            }
            
        except Exception as e:
            return {
                "status": "error",
                "message": f"Failed to start browser service: {str(e)}"
            }
