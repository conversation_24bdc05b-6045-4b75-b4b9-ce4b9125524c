"""
Browser VNC proxy endpoint for manual control
"""

from python.helpers.api import <PERSON><PERSON><PERSON><PERSON><PERSON>, Request, Response
from python.helpers.browser_service import get_browser_service


class BrowserVnc(ApiHandler):
    """Serve noVNC web client for browser control"""
    
    @classmethod
    def requires_auth(cls) -> bool:
        return True
    
    @classmethod
    def requires_csrf(cls) -> bool:
        return True
    
    @classmethod
    def get_methods(cls) -> list[str]:
        return ["GET"]
    
    async def process(self, input: dict, request: Request) -> dict | Response:
        """
        Serve the built-in VNC web client
        """
        try:
            # Get browser service
            browser_service = await get_browser_service()
            
            if not browser_service.is_healthy():
                return Response(
                    "<h1>Browser Service Not Available</h1><p>The browser service is not running. Please enable interactive mode in settings.</p>",
                    503
                )
            
            # Serve the built-in VNC client HTML file
            import os
            vnc_client_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), "webui", "vnc", "vnc-client.html")
            
            if os.path.exists(vnc_client_path):
                with open(vnc_client_path, 'r', encoding='utf-8') as f:
                    vnc_html = f.read()
                return Response(vnc_html, 200, {"Content-Type": "text/html"})
            else:
                return Response(
                    "<h1>VNC Client Not Found</h1><p>The built-in VNC client could not be located. Please check the installation.</p>",
                    404
                )
            
        except Exception as e:
            return Response(
                f"<h1>Error</h1><p>Failed to serve VNC client: {str(e)}</p>",
                500
            )