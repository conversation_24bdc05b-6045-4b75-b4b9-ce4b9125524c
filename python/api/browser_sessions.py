"""
Browser Sessions API - Get information about active browser sessions
"""

from python.helpers.api import <PERSON><PERSON><PERSON><PERSON><PERSON>, Request, Response
import agent as agent_helper
from python.helpers.browser_service import get_browser_service


class BrowserSessions(ApiHandler):
    """Get information about active browser sessions"""
    
    @classmethod
    def requires_auth(cls) -> bool:
        return True
    
    @classmethod
    def requires_csrf(cls) -> bool:
        return True
    
    @classmethod
    def get_methods(cls) -> list[str]:
        return ["GET"]
    
    async def process(self, input: dict, request: Request) -> dict | Response:
        """
        Get list of active browser sessions with VNC information
        """
        try:
            sessions = []
            
            # Get all active agents
            agents = agent_helper.get_agents()
            
            for agent in agents:
                # Check if agent has browser data
                if hasattr(agent, 'data') and agent.data:
                    browser_data = {}

                    # Check for VNC URL
                    if 'browser_vnc_url' in agent.data:
                        browser_data['vnc_url'] = agent.data['browser_vnc_url']

                    # Check for interactive availability
                    if 'browser_interactive_available' in agent.data:
                        browser_data['interactive_available'] = agent.data['browser_interactive_available']

                    # Check for any browser session (including headless)
                    if 'browser_session_active' in agent.data:
                        browser_data['session_active'] = agent.data['browser_session_active']
                        browser_data['mode'] = agent.data.get('browser_mode', 'unknown')

                    # Check for fallback info
                    if 'browser_interactive_fallback' in agent.data:
                        browser_data['interactive_fallback'] = agent.data['browser_interactive_fallback']
                        browser_data['interactive_error'] = agent.data.get('browser_interactive_error', '')

                    # If we have any browser data, add this session
                    if browser_data:
                        session_info = {
                            'session_id': agent.context.id,
                            'agent_name': getattr(agent.context, 'name', f'Agent {agent.context.no}'),
                            'agent_no': agent.context.no,
                            **browser_data
                        }
                        sessions.append(session_info)
            
            # If no sessions found, check if browser service is available
            if not sessions:
                try:
                    browser_service = await get_browser_service()
                    if browser_service and browser_service.is_healthy():
                        connection_info = browser_service.get_connection_info()
                        sessions.append({
                            'session_id': 'browser-service',
                            'agent_name': 'Browser Service',
                            'agent_no': 0,
                            'vnc_url': connection_info.get('novnc_url'),
                            'interactive_available': True
                        })
                except Exception:
                    # Browser service not available, that's ok
                    pass
            
            return {
                "status": "success",
                "sessions": sessions
            }
            
        except Exception as e:
            return {
                "status": "error",
                "message": f"Failed to get browser sessions: {str(e)}"
            }
