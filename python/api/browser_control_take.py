"""
Browser control take endpoint - notify when user takes manual control
"""

from python.helpers.api import <PERSON><PERSON><PERSON><PERSON><PERSON>, Request, Response
import agent as agent_helper


class BrowserControlTake(ApiHandler):
    """Handle notification when user takes manual control of browser"""
    
    @classmethod
    def requires_auth(cls) -> bool:
        return True
    
    @classmethod
    def requires_csrf(cls) -> bool:
        return True
    
    @classmethod
    def get_methods(cls) -> list[str]:
        return ["POST"]
    
    async def process(self, input: dict, request: Request) -> dict | Response:
        """
        Mark that manual control has been taken for a browser session
        """
        try:
            session_id = input.get('session_id')
            
            if not session_id:
                return {
                    "status": "error",
                    "message": "Session ID required"
                }
            
            # Find the agent context with this browser session
            contexts = agent_helper.Agent.contexts
            found = False
            
            for context_id, context in contexts.items():
                agent_data = context.data.get("_browser_agent_state")
                if agent_data:
                    # Mark that manual control is active
                    context.data["browser_manual_control_active"] = True
                    context.data["browser_manual_control_session"] = session_id
                    found = True
                    break
            
            if found:
                return {
                    "status": "success",
                    "message": "Manual control activated",
                    "data": {
                        "session_id": session_id,
                        "status": "active",
                        "timeout": 1800  # 30 minutes
                    }
                }
            else:
                return {
                    "status": "warning",
                    "message": "Session not found, but control request noted",
                    "data": {"session_id": session_id}
                }
                
        except Exception as e:
            return {
                "status": "error",
                "message": f"Failed to activate control: {str(e)}"
            }
