"""
Browser control release endpoint - notify when user releases manual control
"""

from python.helpers.api import <PERSON><PERSON><PERSON><PERSON><PERSON>, Request, Response
import agent as agent_helper


class BrowserControlRelease(ApiHandler):
    """Handle notification when user releases manual control of browser"""
    
    @classmethod
    def requires_auth(cls) -> bool:
        return True
    
    @classmethod
    def requires_csrf(cls) -> bool:
        return True
    
    @classmethod
    def get_methods(cls) -> list[str]:
        return ["POST"]
    
    async def process(self, input: dict, request: Request) -> dict | Response:
        """
        Mark that manual control has been released for a browser session
        """
        try:
            session_id = input.get('session_id')
            
            if not session_id:
                return {
                    "status": "error",
                    "message": "Session ID required"
                }
            
            # Find and update the agent context
            contexts = agent_helper.Agent.contexts
            found = False
            
            for context_id, context in contexts.items():
                if context.data.get("browser_manual_control_session") == session_id:
                    # Mark that manual control is released
                    context.data["browser_manual_control_active"] = False
                    context.data["browser_manual_control_session"] = None
                    found = True
                    break
            
            if found:
                return {
                    "status": "success",
                    "message": "Manual control released",
                    "data": {
                        "session_id": session_id,
                        "status": "released"
                    }
                }
            else:
                return {
                    "status": "warning",
                    "message": "Session not found, but release noted",
                    "data": {"session_id": session_id}
                }
                
        except Exception as e:
            return {
                "status": "error",
                "message": f"Failed to release control: {str(e)}"
            }
