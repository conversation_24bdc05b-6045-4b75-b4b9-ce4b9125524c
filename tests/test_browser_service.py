#!/usr/bin/env python3
"""
Test script for Browser Service Manual Control functionality
Run this to verify the headed browser with VNC access is working correctly
"""

import asyncio
import sys
import os

# Add parent directory to path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from python.helpers.browser_service import BrowserService, get_browser_service
from python.helpers.log import Log


async def test_browser_service():
    """Test the browser service startup and connectivity"""
    
    print("\n" + "="*60)
    print("Browser Service Manual Control Test")
    print("="*60 + "\n")
    
    # Test configuration
    config = {
        "vnc_port": 5900,
        "novnc_port": 6080,
        "cdp_port": 9222,
        "screen_width": 1920,
        "screen_height": 1080
    }
    
    try:
        # Create browser service
        print("1. Creating browser service instance...")
        service = BrowserService(config)
        
        # Start the service
        print("2. Starting browser service components...")
        connection_info = await service.start()
        
        print("\n✅ Browser service started successfully!")
        print("\nConnection Information:")
        print("-" * 40)
        print(f"CDP URL: {connection_info['cdp_url']}")
        print(f"VNC Port: {connection_info['vnc_port']}")
        print(f"noVNC URL: {connection_info['novnc_url']}")
        print(f"Display: {connection_info['display']}")
        print(f"Running: {connection_info['is_running']}")
        print("-" * 40)
        
        # Test health check
        print("\n3. Testing health check...")
        is_healthy = service.is_healthy()
        print(f"Health status: {'✅ Healthy' if is_healthy else '❌ Unhealthy'}")
        
        # Test CDP connection
        print("\n4. Testing Chrome DevTools Protocol connection...")
        try:
            import aiohttp
            async with aiohttp.ClientSession() as session:
                async with session.get(f'http://localhost:{config["cdp_port"]}/json') as resp:
                    if resp.status == 200:
                        data = await resp.json()
                        print(f"✅ CDP is accessible. Found {len(data)} browser targets")
                    else:
                        print(f"❌ CDP returned status: {resp.status}")
        except Exception as e:
            print(f"❌ CDP connection failed: {e}")
        
        # Keep service running for manual testing
        print("\n" + "="*60)
        print("🎉 SUCCESS! Browser service is running.")
        print("\nYou can now:")
        print(f"1. Open VNC viewer and connect to: vnc://localhost:{config['vnc_port']}")
        print(f"2. Open browser and navigate to: {connection_info['novnc_url']}")
        print(f"3. Access Chrome DevTools at: http://localhost:{config['cdp_port']}")
        print("\nPress Ctrl+C to stop the service...")
        print("="*60 + "\n")
        
        # Wait for user to stop
        try:
            await asyncio.Event().wait()
        except KeyboardInterrupt:
            pass
        
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        # Clean up
        print("\nStopping browser service...")
        await service.stop()
        print("Service stopped.")
    
    return True


async def test_singleton():
    """Test singleton pattern of browser service"""
    print("\nTesting singleton pattern...")
    
    service1 = await get_browser_service()
    service2 = await get_browser_service()
    
    if service1 is service2:
        print("✅ Singleton pattern working correctly")
    else:
        print("❌ Singleton pattern failed")
    
    return service1 is service2


async def main():
    """Run all tests"""
    
    # Test basic functionality
    success = await test_browser_service()
    
    if success:
        # Test singleton
        await test_singleton()
        
        print("\n✅ All tests passed!")
    else:
        print("\n❌ Some tests failed")
        sys.exit(1)


if __name__ == "__main__":
    # Check if running in Docker
    if not os.path.exists("/.dockerenv") and not os.environ.get("DOCKER_CONTAINER"):
        print("\n⚠️  WARNING: This test is designed to run inside the Docker container.")
        print("It requires Xvfb, VNC, and Chrome to be installed.")
        print("\nTo run the test:")
        print("1. Build the Docker image with the updated Dockerfile")
        print("2. Run the container: docker run -p 5900:5900 -p 6080:6080 -p 9222:9222 agent-zero")
        print("3. Execute this test inside the container")
        response = input("\nDo you want to continue anyway? (y/n): ")
        if response.lower() != 'y':
            sys.exit(0)
    
    asyncio.run(main())