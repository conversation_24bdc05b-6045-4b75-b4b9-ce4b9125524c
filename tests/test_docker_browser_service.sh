#!/bin/bash
# Docker Browser Service Test Script
# This script tests the browser manual control functionality inside Docker

set -e

echo "🐳 Testing Browser Manual Control in Docker Container"
echo "================================================="

# Test 1: Check if required packages are installed
echo "📦 Test 1: Checking required packages..."

check_package() {
    if command -v "$1" >/dev/null 2>&1; then
        echo "  ✅ $1 is installed"
    else
        echo "  ❌ $1 is NOT installed"
        exit 1
    fi
}

check_package "Xvfb"
check_package "x11vnc"
check_package "websockify"
check_package "fluxbox"

# Test 2: Check if VNC web interface is properly set up
echo -e "\n📁 Test 2: Checking VNC web interface setup..."
if [ -d "/usr/share/novnc" ]; then
    echo "  ✅ VNC web directory exists"
    if [ -f "/usr/share/novnc/vnc.html" ]; then
        echo "  ✅ VNC web compatibility file found"
    else
        echo "  ❌ VNC web compatibility file NOT found"
        exit 1
    fi
else
    echo "  ❌ VNC web directory NOT found"
    exit 1
fi

# Test 2b: Check built-in VNC client
echo -e "\n📱 Test 2b: Checking built-in VNC client..."
if [ -f "/a0/webui/vnc/vnc-client.html" ]; then
    echo "  ✅ Built-in VNC client found"
else
    echo "  ❌ Built-in VNC client NOT found"
    exit 1
fi

# Test 3: Check if Chrome/Chromium is available
echo -e "\n🌐 Test 3: Checking Chrome/Chromium installation..."
CHROME_FOUND=false
for chrome in chromium-browser chromium google-chrome google-chrome-stable; do
    if command -v "$chrome" >/dev/null 2>&1; then
        echo "  ✅ Found: $chrome at $(which $chrome)"
        CHROME_FOUND=true
        break
    fi
done

if [ "$CHROME_FOUND" = false ]; then
    echo "  ❌ No Chrome/Chromium found!"
    exit 1
fi

# Test 4: Test Xvfb startup
echo -e "\n🖥️ Test 4: Testing Xvfb startup..."
export DISPLAY=:99
if Xvfb :99 -screen 0 1920x1080x24 -ac +extension GLX +render -noreset >/dev/null 2>&1 &
then
    XVFB_PID=$!
    sleep 2
    if ps -p $XVFB_PID > /dev/null; then
        echo "  ✅ Xvfb started successfully (PID: $XVFB_PID)"
        kill $XVFB_PID
        wait $XVFB_PID 2>/dev/null || true
    else
        echo "  ❌ Xvfb failed to start"
        exit 1
    fi
else
    echo "  ❌ Xvfb command failed"
    exit 1
fi

# Test 5: Test VNC server startup
echo -e "\n📺 Test 5: Testing VNC server startup..."
Xvfb :99 -screen 0 1920x1080x24 -ac +extension GLX +render -noreset >/dev/null 2>&1 &
XVFB_PID=$!
sleep 2

if x11vnc -display :99 -nopw -forever -shared -rfbport 5900 -bg >/dev/null 2>&1; then
    sleep 2
    if pgrep x11vnc >/dev/null; then
        echo "  ✅ VNC server started successfully"
        pkill x11vnc || true
    else
        echo "  ❌ VNC server failed to start"
        kill $XVFB_PID 2>/dev/null || true
        exit 1
    fi
else
    echo "  ❌ VNC server command failed"
    kill $XVFB_PID 2>/dev/null || true
    exit 1
fi

kill $XVFB_PID 2>/dev/null || true
wait $XVFB_PID 2>/dev/null || true

# Test 6: Test websockify startup
echo -e "\n🔌 Test 6: Testing websockify startup..."
Xvfb :99 -screen 0 1920x1080x24 -ac +extension GLX +render -noreset >/dev/null 2>&1 &
XVFB_PID=$!
sleep 2

x11vnc -display :99 -nopw -forever -shared -rfbport 5900 -bg >/dev/null 2>&1
sleep 2

if websockify --web /usr/share/novnc 6080 localhost:5900 >/dev/null 2>&1 &
then
    WEBSOCKIFY_PID=$!
    sleep 3
    if ps -p $WEBSOCKIFY_PID > /dev/null; then
        echo "  ✅ websockify started successfully (PID: $WEBSOCKIFY_PID)"
        kill $WEBSOCKIFY_PID 2>/dev/null || true
    else
        echo "  ❌ websockify failed to start"
        pkill x11vnc || true
        kill $XVFB_PID 2>/dev/null || true
        exit 1
    fi
else
    echo "  ❌ websockify command failed"
    pkill x11vnc || true
    kill $XVFB_PID 2>/dev/null || true
    exit 1
fi

# Clean up
pkill x11vnc || true
kill $XVFB_PID 2>/dev/null || true
wait $XVFB_PID 2>/dev/null || true

# Test 7: Test Chrome startup with debugging
echo -e "\n🚀 Test 7: Testing Chrome startup with debugging..."
Xvfb :99 -screen 0 1920x1080x24 -ac +extension GLX +render -noreset >/dev/null 2>&1 &
XVFB_PID=$!
export DISPLAY=:99
sleep 2

# Find Chrome binary
CHROME_BIN=""
for chrome in chromium-browser chromium google-chrome google-chrome-stable; do
    if command -v "$chrome" >/dev/null 2>&1; then
        CHROME_BIN="$chrome"
        break
    fi
done

if [ -n "$CHROME_BIN" ]; then
    # Start Chrome with debugging
    $CHROME_BIN \
        --remote-debugging-port=9222 \
        --remote-debugging-address=0.0.0.0 \
        --user-data-dir=/tmp/test_browser_profile \
        --no-first-run \
        --no-default-browser-check \
        --disable-default-apps \
        --disable-dev-shm-usage \
        --disable-gpu \
        --no-sandbox \
        --disable-setuid-sandbox \
        --window-size=1920,1080 \
        --window-position=0,0 \
        about:blank >/dev/null 2>&1 &
    
    CHROME_PID=$!
    sleep 5
    
    # Test if Chrome debugging is accessible
    if curl -s http://localhost:9222/json >/dev/null 2>&1; then
        echo "  ✅ Chrome started with debugging port accessible"
        kill $CHROME_PID 2>/dev/null || true
    else
        echo "  ❌ Chrome debugging port not accessible"
        kill $CHROME_PID 2>/dev/null || true
        kill $XVFB_PID 2>/dev/null || true
        exit 1
    fi
else
    echo "  ❌ Chrome binary not found"
    kill $XVFB_PID 2>/dev/null || true
    exit 1
fi

kill $XVFB_PID 2>/dev/null || true
wait $XVFB_PID 2>/dev/null || true
rm -rf /tmp/test_browser_profile

# Test 8: Test browser service script
echo -e "\n📋 Test 8: Testing browser service script..."
if [ -f "/exe/run_browser_service.sh" ]; then
    if [ -x "/exe/run_browser_service.sh" ]; then
        echo "  ✅ Browser service script exists and is executable"
    else
        echo "  ❌ Browser service script not executable"
        exit 1
    fi
else
    echo "  ❌ Browser service script not found"
    exit 1
fi

# Test 9: Test Python browser service imports
echo -e "\n🐍 Test 9: Testing Python browser service imports..."
python3 -c "
try:
    from python.helpers.browser_service import BrowserService, get_browser_service
    print('  ✅ Browser service imports successful')
except ImportError as e:
    print(f'  ❌ Browser service import failed: {e}')
    exit(1)
" || exit 1

# Test 10: Test ports are available
echo -e "\n🔌 Test 10: Testing required ports availability..."
check_port() {
    if netstat -tuln 2>/dev/null | grep -q ":$1 "; then
        echo "  ⚠️  Port $1 is already in use"
    else
        echo "  ✅ Port $1 is available"
    fi
}

check_port 5900  # VNC
check_port 6080  # noVNC
check_port 9222  # Chrome debugging

echo -e "\n🎉 All Docker Browser Service tests completed successfully!"
echo "==============================================="
echo ""
echo "Next steps:"
echo "1. Build Docker image with: docker build -f docker/run/Dockerfile -t agent-zero-interactive ."
echo "2. Run with ports exposed: docker run -p 50001:80 -p 5900:5900 -p 6080:6080 -p 9222:9222 agent-zero-interactive"
echo "3. Enable interactive mode in Agent Zero settings"
echo "4. Test browser manual control functionality"
echo ""