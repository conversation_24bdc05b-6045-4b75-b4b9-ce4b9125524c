# Browser State Synchronization Test

## Overview

This document outlines the test procedure for Story 3: State Synchronization of the Browser Manual Control PoC. This tests the seamless handoff between autonomous and manual browser operation.

## Prerequisites

1. **Complete Setup from Stories 1 & 2**
   - Docker container with browser service running
   - Interactive mode enabled in settings
   - Frontend controls working

2. **Test Environment**
   ```bash
   docker run -p 50001:80 -p 5900:5900 -p 6080:6080 -p 9222:9222 agent-zero-interactive
   ```

## Test Scenarios

### Scenario 1: Basic Pause and Resume

**Objective:** Verify agent pauses when user takes control and resumes when released

**Setup:**
1. Start browser agent task: "Navigate to google.com and search for 'agent zero'"
2. Wait for agent to navigate to Google

**Test Steps:**
1. During agent execution, click "Take Manual Control"
2. Verify agent pauses execution
3. Perform manual actions (e.g., change search term)
4. Release control
5. Verify agent resumes and acknowledges changes

**Expected Results:**
- ✅ Agent execution pauses immediately when control taken
- ✅ Console shows: "🖱️ Manual control activated. Pausing agent execution..."
- ✅ Agent waits indefinitely until control released
- ✅ Console shows: "🤖 Manual control released. Resuming agent execution..."
- ✅ Agent acknowledges page state changes
- ✅ Agent continues task with updated context

**Pass/Fail:** ___

### Scenario 2: State Synchronization

**Objective:** Verify browser state is properly synchronized after manual control

**Setup:**
1. Start browser agent task: "Fill out the contact form on example.com"
2. Wait for agent to navigate to a form

**Test Steps:**
1. Take manual control during form filling
2. Manually fill some form fields
3. Change the current page (navigate somewhere else)
4. Release control
5. Observe agent's understanding of new state

**Expected Results:**
- ✅ Agent detects page URL change
- ✅ Agent recognizes form fields that were filled
- ✅ Console shows: "📄 State synchronized. Current page: [new page title]"
- ✅ Agent adapts strategy based on new state
- ✅ Selector map updated for new page

**Pass/Fail:** ___

### Scenario 3: Long-Running Manual Control

**Objective:** Verify system handles extended manual control sessions

**Setup:**
1. Start any browser agent task
2. Take manual control immediately

**Test Steps:**
1. Keep manual control active for 5+ minutes
2. Perform various actions (navigation, form filling, etc.)
3. Check agent remains paused
4. Release control
5. Verify clean resumption

**Expected Results:**
- ✅ Agent remains paused for entire duration
- ✅ No timeout errors or crashes
- ✅ VNC session remains stable
- ✅ Timer shows correct elapsed time
- ✅ Agent resumes cleanly after release
- ✅ Memory usage doesn't grow excessively

**Pass/Fail:** ___

### Scenario 4: Manual Control During Error Conditions

**Objective:** Test manual control when agent encounters errors

**Setup:**
1. Start browser agent task with intentional error (e.g., "Login to nonexistent-site-123.com")
2. Wait for agent to encounter error

**Test Steps:**
1. When agent shows error or struggles, take manual control
2. Navigate to a valid site manually
3. Release control
4. Observe agent behavior

**Expected Results:**
- ✅ Manual control works even during agent errors
- ✅ Agent recovers from error state after manual intervention
- ✅ Agent continues with new valid context
- ✅ No system crashes or hangs

**Pass/Fail:** ___

### Scenario 5: Multiple Control Sessions

**Objective:** Test taking and releasing control multiple times in one session

**Setup:**
1. Start browser agent task: "Research the latest news about AI"

**Test Steps:**
1. Take manual control → perform action → release
2. Wait for agent to continue
3. Take manual control again → perform different action → release
4. Repeat 2-3 times
5. Let agent complete the task

**Expected Results:**
- ✅ Each control session works independently
- ✅ Agent adapts to each manual change
- ✅ No memory leaks or state corruption
- ✅ Clean transitions every time
- ✅ Agent completes task successfully

**Pass/Fail:** ___

### Scenario 6: Control Cancellation

**Objective:** Test canceling the agent task while manual control is active

**Setup:**
1. Start browser agent task
2. Take manual control

**Test Steps:**
1. While manual control is active, stop/cancel the agent task
2. Observe system behavior
3. Check for resource cleanup

**Expected Results:**
- ✅ Agent task cancels cleanly
- ✅ Manual control session ends gracefully
- ✅ VNC connection closes properly
- ✅ No orphaned processes or connections
- ✅ System returns to ready state

**Pass/Fail:** ___

## API Integration Tests

### Test Manual Control Endpoints

```bash
# Test session lifecycle
curl -X POST -H "Content-Type: application/json" \
     -H "X-CSRF-Token: [token]" \
     -d '{"session_id":"test-sync-session"}' \
     "http://localhost:50001/browser_control_take"

# Verify agent pauses (check logs)

curl -X POST -H "Content-Type: application/json" \
     -H "X-CSRF-Token: [token]" \
     -d '{"session_id":"test-sync-session"}' \
     "http://localhost:50001/browser_control_release"

# Verify agent resumes (check logs)
```

## Performance Tests

### Memory and Resource Usage

**During Manual Control:**
- [ ] Memory usage stable
- [ ] CPU usage reasonable
- [ ] No memory leaks
- [ ] Browser process stable

**During State Sync:**
- [ ] State sync completes < 5 seconds
- [ ] Screenshot updates promptly
- [ ] Selector map rebuilds efficiently

## Error Handling Tests

### Network Issues
- [ ] VNC connection loss handled gracefully
- [ ] Agent recovers from browser disconnection
- [ ] Manual control timeouts work correctly

### Browser Crashes
- [ ] Browser process crash during manual control
- [ ] Agent handles browser restart
- [ ] State recovery after browser restart

## Logging and Monitoring

### Expected Log Messages

**Control Activation:**
```
🖱️ Manual control activated for session [id]. Pausing agent execution...
Manual browser control activated. Agent paused for user intervention.
```

**Control Release:**
```
🤖 Manual control released. Resuming agent execution...
Manual browser control released. Agent resuming autonomous operation.
```

**State Synchronization:**
```
📄 State synchronized. Current page: [title] ([url])
Browser state synchronized after manual control. Page: [title]
```

**Warnings/Errors:**
```
⚠️ Failed to sync state after manual control: [error]
Warning: Failed to sync browser state after manual control: [error]
```

## Integration Test Matrix

| Scenario | Agent State | Manual Action | Expected Result |
|----------|-------------|---------------|-----------------|
| Normal Navigation | Navigating | Take control, navigate elsewhere | Agent continues from new page |
| Form Filling | Filling form | Take control, fill fields | Agent recognizes filled fields |
| Error State | Error encountered | Take control, fix issue | Agent recovers and continues |
| Task Completion | Nearly done | Take control, complete task | Agent recognizes completion |
| Page Loading | Waiting for page | Take control during load | Agent waits for load completion |

## Browser Compatibility

Test in multiple browsers for VNC client:
- [ ] Chrome/Chromium
- [ ] Firefox  
- [ ] Safari (macOS)
- [ ] Edge

## Stress Tests

### Rapid Control Changes
- [ ] Take/release control rapidly 10 times
- [ ] No system instability
- [ ] No JavaScript errors

### Large Page Changes
- [ ] Navigate to complex pages during manual control
- [ ] Upload files during manual control
- [ ] Handle JavaScript-heavy sites

## Test Results Summary

**Test Scenarios:** 6  
**Passed:** ___  
**Failed:** ___  
**Blocked:** ___  

**Performance Tests:** ___/___  
**Error Handling Tests:** ___/___  
**Integration Tests:** ___/___  

## Critical Issues Found

(List any issues that prevent core state synchronization functionality)

1. ________________________________
2. ________________________________
3. ________________________________

## Minor Issues Found

(List cosmetic or minor functionality issues)

1. ________________________________
2. ________________________________
3. ________________________________

## Overall Assessment

- [ ] **PASS** - State synchronization working perfectly
- [ ] **PASS** - Minor issues found but core functionality works  
- [ ] **FAIL** - Major issues prevent proper state synchronization
- [ ] **BLOCKED** - Cannot complete testing due to setup issues

## Recommendations

### For Production Release
- [ ] Add automatic state verification
- [ ] Implement state recovery mechanisms
- [ ] Add user confirmation for major state changes
- [ ] Enhance error reporting for sync failures

### For Future Enhancement
- [ ] Visual diff showing changes made during manual control
- [ ] Undo capability for manual changes
- [ ] Smart intervention suggestions based on page content
- [ ] Collaborative control (multiple users)

## Test Environment Details

**Date:** _______________  
**Tester:** _______________  
**Agent Zero Version:** _______________  
**Docker Image:** _______________  
**Browser:** _______________  

**Notes:**
_________________________________
_________________________________
_________________________________