# Browser Control UI Integration Test

## Overview

This document outlines the test procedure for Story 2: Frontend Control Integration of the Browser Manual Control PoC.

## Prerequisites

1. **Docker Container Running**
   ```bash
   docker build -f docker/run/Dockerfile -t agent-zero-interactive . --build-arg BRANCH=main
   docker run -p 50001:80 -p 5900:5900 -p 6080:6080 -p 9222:9222 agent-zero-interactive
   ```

2. **Interactive Mode Enabled**
   - Navigate to http://localhost:50001
   - Go to Settings → Browser Model
   - Enable "Interactive Mode" toggle
   - Save settings

## Test Cases

### TC1: Control Button Appearance

**Objective:** Verify the "Take Manual Control" button appears on browser agent messages

**Steps:**
1. Start a browser agent task (e.g., "Browse to google.com")
2. Wait for browser agent message to appear
3. Look for browser control button in the message

**Expected Results:**
- ✅ Browser agent message displays normally
- ✅ Control button appears with touch_app icon and "Take Manual Control" text
- ✅ Status text shows "(Interactive mode available)"
- ✅ Button has proper styling (blue gradient, hover effects)

**Pass/Fail:** ___

### TC2: Modal Opening

**Objective:** Verify clicking the control button opens the VNC modal

**Steps:**
1. Click the "Take Manual Control" button
2. Observe modal behavior

**Expected Results:**
- ✅ Modal opens with title "Browser Manual Control"
- ✅ Modal is sized appropriately (95% width, 90% height)
- ✅ Modal contains VNC iframe
- ✅ Header shows control timer starting at 00:00
- ✅ "Release Control" button is visible

**Pass/Fail:** ___

### TC3: VNC Connection

**Objective:** Verify VNC iframe loads and shows connection attempt

**Steps:**
1. With modal open, inspect the VNC iframe
2. Check browser developer tools for any errors

**Expected Results:**
- ✅ Iframe loads without errors
- ✅ VNC connection interface appears
- ✅ Connection status shows "Connecting..." initially
- ✅ No JavaScript errors in console

**Pass/Fail:** ___

### TC4: Timer Functionality

**Objective:** Verify the control timer updates correctly

**Steps:**
1. Open browser control modal
2. Wait 10 seconds
3. Observe timer display

**Expected Results:**
- ✅ Timer starts at 00:00
- ✅ Timer increments every second
- ✅ Timer format is MM:SS
- ✅ Timer displays correctly (e.g., 00:10 after 10 seconds)

**Pass/Fail:** ___

### TC5: Release Control

**Objective:** Verify releasing control works properly

**Steps:**
1. Open browser control modal
2. Click "Release Control" button
3. Observe behavior

**Expected Results:**
- ✅ Modal closes immediately
- ✅ API call made to /browser_control_release
- ✅ No JavaScript errors
- ✅ Timer stops

**Pass/Fail:** ___

### TC6: Modal Close (X button)

**Objective:** Verify closing modal via X button triggers release

**Steps:**
1. Open browser control modal
2. Click the X (close) button in modal header
3. Observe behavior

**Expected Results:**
- ✅ Modal closes
- ✅ Release control API called automatically
- ✅ Timer stops
- ✅ No JavaScript errors

**Pass/Fail:** ___

### TC7: CSRF Protection

**Objective:** Verify CSRF tokens are properly sent

**Steps:**
1. Open browser developer tools → Network tab
2. Take manual control
3. Inspect API requests

**Expected Results:**
- ✅ X-CSRF-Token header present in control API calls
- ✅ Token parameter present in VNC iframe URL
- ✅ API calls return 200 (not 403 Forbidden)

**Pass/Fail:** ___

### TC8: Responsive Design

**Objective:** Verify UI works on different screen sizes

**Steps:**
1. Test on desktop (1920x1080)
2. Test on tablet (768px width)
3. Test on mobile (400px width)

**Expected Results:**
- ✅ Control button visible and clickable on all sizes
- ✅ Modal scales appropriately
- ✅ Control header stacks on mobile
- ✅ Text remains readable

**Pass/Fail:** ___

### TC9: Multiple Browser Sessions

**Objective:** Verify handling of multiple browser agent sessions

**Steps:**
1. Start two browser agent tasks
2. Check if control buttons appear for both
3. Try taking control of one

**Expected Results:**
- ✅ Control button appears for each session
- ✅ Each button has unique session ID
- ✅ Taking control of one doesn't affect others
- ✅ No JavaScript errors

**Pass/Fail:** ___

### TC10: Error Handling

**Objective:** Verify graceful handling of service unavailable

**Steps:**
1. Stop browser service (if possible)
2. Try to take manual control
3. Observe error handling

**Expected Results:**
- ✅ Appropriate error message shown
- ✅ No JavaScript crashes
- ✅ Modal handles service unavailable gracefully
- ✅ User can close error modal

**Pass/Fail:** ___

## API Endpoint Testing

### Manual API Tests

Test the API endpoints directly using curl or browser dev tools:

```bash
# Test VNC proxy endpoint
curl -H "X-CSRF-Token: [token]" "http://localhost:50001/browser_vnc"

# Test take control
curl -X POST -H "Content-Type: application/json" \
     -H "X-CSRF-Token: [token]" \
     -d '{"session_id":"test-session"}' \
     "http://localhost:50001/browser_control_take"

# Test release control
curl -X POST -H "Content-Type: application/json" \
     -H "X-CSRF-Token: [token]" \
     -d '{"session_id":"test-session"}' \
     "http://localhost:50001/browser_control_release"
```

## Browser Compatibility

Test in multiple browsers:
- [ ] Chrome/Chromium
- [ ] Firefox
- [ ] Safari (if on macOS)
- [ ] Edge

## Performance Checks

- [ ] Modal opens within 2 seconds
- [ ] VNC iframe loads within 5 seconds
- [ ] No memory leaks after multiple open/close cycles
- [ ] Timer updates smoothly without lag

## Security Verification

- [ ] CSRF tokens required for all control operations
- [ ] VNC access requires authentication
- [ ] No sensitive information exposed in client-side code
- [ ] Session IDs are not predictable

## Test Results Summary

**Total Test Cases:** 10  
**Passed:** ___  
**Failed:** ___  
**Blocked:** ___  

**Critical Issues Found:**
(List any critical issues that prevent core functionality)

**Minor Issues Found:**
(List cosmetic or minor functionality issues)

**Overall Assessment:**
- [ ] Ready for Story 3 implementation
- [ ] Needs minor fixes before proceeding
- [ ] Needs major fixes before proceeding

## Notes

**Test Environment:**
- Date: ___________
- Agent Zero Version: ___________
- Docker Image: ___________
- Browser: ___________

**Additional Comments:**
_____________________
_____________________
_____________________