#!/usr/bin/env python3
"""
Test script to verify Browser Agent Interactive Mode integration
"""

import asyncio
import json
import time
from pathlib import Path

# Add the project root to Python path
import sys
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from initialize import initialize_agent
from agent import Agent, AgentContext
from python.helpers import settings
from python.tools.browser_agent import BrowserAgent


async def test_browser_interactive_mode():
    """Test that browser agent can connect to interactive mode"""
    
    print("🧪 Testing Browser Agent Interactive Mode Integration")
    print("=" * 60)
    
    # Check settings
    current_settings = settings.get_settings()
    interactive_mode = current_settings.get("browser_interactive_mode", False)
    
    print(f"📋 Interactive Mode Setting: {interactive_mode}")
    print(f"📋 VNC Port: {current_settings.get('browser_vnc_port', 'N/A')}")
    print(f"📋 noVNC Port: {current_settings.get('browser_novnc_port', 'N/A')}")
    print(f"📋 CDP Port: {current_settings.get('browser_cdp_port', 'N/A')}")
    
    if not interactive_mode:
        print("❌ Interactive mode is not enabled in settings!")
        return False
    
    # Initialize agent configuration
    print("\n🔧 Initializing Agent Configuration...")
    try:
        config = initialize_agent()
        print(f"✅ Agent config loaded. Browser config: {getattr(config, 'browser', 'Not found')}")
    except Exception as e:
        print(f"❌ Failed to initialize agent: {e}")
        return False
    
    # Create a test agent context
    print("\n🤖 Creating Test Agent Context...")
    try:
        context = AgentContext(config=config)
        agent = Agent(number=1, context=context, config=config)
        print(f"✅ Agent created with ID: {agent.context.id}")
    except Exception as e:
        print(f"❌ Failed to create agent: {e}")
        return False
    
    # Test browser service integration directly
    print("\n🌐 Testing Browser Service Integration...")
    try:
        # Import the browser service directly
        from python.helpers.browser_service import get_browser_service
        
        # Get browser config from agent
        browser_config = getattr(config, 'browser', {})
        print(f"📋 Browser config: {browser_config}")
        
        # Test browser service creation
        browser_service = await get_browser_service(browser_config)
        print("✅ Browser service instance created")
        
        # Check if service is healthy (already running from our manual setup)
        if browser_service.is_healthy():
            print("✅ Browser service is healthy and running")
            connection_info = browser_service.get_connection_info()
            print(f"✅ Connection info: {connection_info}")
        else:
            print("⚠️  Browser service not running, this is expected for first-time setup")
        
        # Test CDP connection directly if service is available
        try:
            import aiohttp
            async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=3)) as session:
                async with session.get('http://localhost:9222/json/list') as resp:
                    if resp.status == 200:
                        tabs = await resp.json()
                        print(f"✅ CDP connection working, found {len(tabs)} browser tabs")
                    else:
                        print(f"⚠️  CDP returned status {resp.status}")
        except Exception as e:
            print(f"ℹ️  CDP test skipped: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ Browser service test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_connection_urls():
    """Test that the connection URLs are accessible"""
    
    print("\n🔗 Testing Connection URLs...")
    print("-" * 40)
    
    import aiohttp
    
    urls_to_test = [
        ("Agent Zero UI", "http://localhost:7178"),
        ("noVNC Interface", "http://localhost:6080/vnc.html"),
        ("Chrome DevTools", "http://localhost:9222/json"),
    ]
    
    for name, url in urls_to_test:
        try:
            async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=5)) as session:
                async with session.get(url) as response:
                    if response.status == 200:
                        print(f"✅ {name}: {url} - Status {response.status}")
                    else:
                        print(f"⚠️  {name}: {url} - Status {response.status}")
        except Exception as e:
            print(f"❌ {name}: {url} - Connection failed: {e}")


def main():
    print("🚀 Browser Agent Interactive Mode Integration Test")
    print("=" * 70)
    
    success = True
    
    # Run async tests
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    
    try:
        success = loop.run_until_complete(test_browser_interactive_mode())
        if success:
            loop.run_until_complete(test_connection_urls())
    except Exception as e:
        print(f"❌ Test suite failed: {e}")
        success = False
    finally:
        loop.close()
    
    print("\n" + "=" * 70)
    if success:
        print("🎉 Browser Agent Interactive Mode Integration Test PASSED!")
        print("✅ The browser agent is properly configured for interactive mode")
        print("✅ Connection URLs are accessible")
        print("\n🎯 Next Steps:")
        print("1. Open Agent Zero UI: http://localhost:7178")
        print("2. Start a browser agent task")
        print("3. Look for 'Take Manual Control' button in browser messages")
        print("4. Test manual control via noVNC: http://localhost:6080/vnc.html")
        print("5. Verify seamless switching between automated and manual control")
    else:
        print("❌ Browser Agent Interactive Mode Integration Test FAILED!")
        print("🔧 Please check the configuration and container setup")
    
    print("=" * 70)
    return success


if __name__ == "__main__":
    exit_code = 0 if main() else 1
    sys.exit(exit_code)
