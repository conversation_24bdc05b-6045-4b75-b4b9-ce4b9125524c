# Browser Manual Control Feature

## Overview

The Browser Manual Control feature allows users to take direct control of the browser agent's browser session through a web-based VNC interface. This is particularly useful for handling CAPTCHAs, complex login forms, and other interactions that require human intervention.

## Architecture

### Components

1. **Browser Service (`python/helpers/browser_service.py`)**
   - Manages the lifecycle of headed browser instances
   - Starts and manages Xvfb, VNC server, and Chrome with debugging
   - Provides connection information for external access

2. **Enhanced BrowserAgent (`python/tools/browser_agent.py`)**
   - Modified to support both headless and headed modes
   - Connects to external browser via Chrome DevTools Protocol (CDP)
   - Falls back to headless mode if interactive mode fails

3. **Configuration Settings (`python/helpers/settings.py`)**
   - New settings for enabling interactive mode
   - Configurable ports for VNC, noVNC, and CDP
   - Integrated into Agent Zero's settings UI

4. **Docker Support**
   - Updated Dockerfile with Xvfb, VNC, and noVNC packages
   - Exposed ports for browser control (5900, 6080, 9222)
   - Startup script for browser service

## Installation

### 1. Build Docker Image

```bash
cd agent-zero
docker build -f docker/run/Dockerfile -t agent-zero-bc .
```

### 2. Run Container with Exposed Ports

```bash
docker run -d \
  -p 50001:80 \
  -p 5900:5900 \
  -p 6080:6080 \
  -p 9222:9222 \
  --name agent-zero-bc \
  agent-zero-bc
```

## Configuration

### Enable Interactive Mode

1. Open Agent Zero Web UI (http://localhost:50001)
2. Go to Settings → Browser Model
3. Enable "Interactive Mode" toggle
4. Configure ports if needed (defaults are usually fine):
   - VNC Port: 5900
   - noVNC Web Port: 6080
   - Chrome DevTools Port: 9222
5. Save settings

## Usage

### Automatic Mode

When interactive mode is enabled, the browser agent will automatically:
1. Start a headed browser with VNC access
2. Connect via CDP instead of creating a new browser
3. Provide VNC access information in the UI

### Manual Control

When the browser agent encounters a situation requiring manual intervention:

1. **Via Web Interface (Recommended)**
   - Look for browser agent messages in the chat
   - The message will include VNC connection info
   - Open http://localhost:6080/vnc.html in your browser
   - You'll see the browser window and can interact directly

2. **Via VNC Client**
   - Use any VNC client (RealVNC, TightVNC, etc.)
   - Connect to `vnc://localhost:5900`
   - No password required (can be configured if needed)

3. **Via Chrome DevTools**
   - Open http://localhost:9222 for debugging
   - Access Chrome DevTools interface directly

### Example Workflow

1. User asks Agent Zero to "Login to my account at example.com"
2. Browser agent navigates to the site
3. If CAPTCHA detected, agent pauses
4. User takes control via noVNC web interface
5. User solves CAPTCHA and enters credentials
6. User releases control
7. Agent continues with the task

## Testing

### Run Test Script

Inside the Docker container:

```bash
python tests/test_browser_service.py
```

This will:
- Start the browser service
- Verify all components are running
- Test CDP connectivity
- Provide connection URLs for manual testing

### Manual Testing

1. Enable interactive mode in settings
2. Run a browser agent task
3. Verify VNC is accessible at http://localhost:6080/vnc.html
4. Interact with the browser through VNC
5. Confirm agent continues after manual intervention

## Troubleshooting

### Browser Service Won't Start

- Check Docker logs: `docker logs agent-zero-bc`
- Verify ports are not in use: `lsof -i :5900,6080,9222`
- Ensure Docker has enough resources allocated

### VNC Connection Refused

- Verify VNC server is running: `ps aux | grep x11vnc`
- Check firewall settings
- Ensure ports are properly exposed in Docker

### Browser Not Visible in VNC

- Check Xvfb is running: `ps aux | grep Xvfb`
- Verify DISPLAY environment variable: `echo $DISPLAY`
- Restart browser service: `/exe/run_browser_service.sh`

### Fallback to Headless Mode

If interactive mode fails, the system automatically falls back to headless mode. Check logs for the reason:
- Chrome/Chromium not found
- Ports already in use
- Missing dependencies

## Security Considerations

### Current Implementation (PoC)

- No VNC password (for ease of testing)
- Ports exposed directly
- Basic CSRF protection planned for production

### Production Recommendations

1. **Add VNC Authentication**
   ```bash
   x11vnc -storepasswd yourpassword /tmp/vncpasswd
   x11vnc -rfbauth /tmp/vncpasswd ...
   ```

2. **Use Reverse Proxy**
   - Put noVNC behind nginx/Apache
   - Add SSL/TLS encryption
   - Implement proper authentication

3. **Network Isolation**
   - Use Docker networks
   - Restrict port access
   - Implement firewall rules

4. **Session Management**
   - Add timeout for manual control sessions
   - Log all manual interventions
   - Implement user access controls

## Future Enhancements (Story 2 & 3)

### Story 2: Frontend Control Integration
- Add "Take Control" button to browser messages
- Integrate noVNC in modal popup
- Real-time status updates

### Story 3: State Synchronization
- Pause agent during manual control
- Seamless handoff between modes
- State recovery after intervention

## API Reference

### Browser Service

```python
from python.helpers.browser_service import get_browser_service

# Get or create service
service = await get_browser_service(config)

# Start service
connection_info = await service.start()

# Check health
is_healthy = service.is_healthy()

# Stop service
await service.stop()
```

### Connection Info Structure

```python
{
    "cdp_url": "ws://localhost:9222",
    "vnc_port": 5900,
    "novnc_url": "http://localhost:6080/vnc.html",
    "display": ":99",
    "is_running": True
}
```

## Contributing

To contribute to this feature:

1. Follow the existing patterns in Agent Zero
2. Test thoroughly in Docker environment
3. Update documentation for any changes
4. Consider security implications

## License

This feature is part of Agent Zero and follows the same license terms.