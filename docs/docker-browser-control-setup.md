# Docker Browser Manual Control Setup Guide

## 🐳 Critical Docker Setup for Browser Manual Control

This guide ensures the Browser Manual Control feature works correctly in the Docker container environment.

## Quick Start

### 1. Build the Enhanced Docker Image

```bash
# Navigate to Agent Zero directory
cd agent-zero

# Build with browser manual control support
docker build -f docker/run/Dockerfile -t agent-zero-bc . --build-arg BRANCH=main

# Alternative: Build with custom tag
docker build -f docker/run/Dockerfile -t agent-zero:browser-control . --build-arg BRANCH=main
```

### 2. Run Container with Required Ports

```bash
# Run with all necessary ports exposed
docker run -d \
  --name agent-zero-browser \
  -p 50001:80 \
  -p 5900:5900 \
  -p 6080:6080 \
  -p 9222:9222 \
  agent-zero-bc

# Alternative: Interactive mode for debugging
docker run -it \
  --name agent-zero-browser \
  -p 50001:80 \
  -p 5900:5900 \
  -p 6080:6080 \
  -p 9222:9222 \
  agent-zero-bc
```

### 3. Port Mapping Explanation

| Host Port | Container Port | Service | Purpose |
|-----------|----------------|---------|---------|
| 50001 | 80 | Agent Zero Web UI | Main application interface |
| 5900 | 5900 | VNC Server | Direct VNC client access |
| 6080 | 6080 | noVNC Web | Browser-based VNC client |
| 9222 | 9222 | Chrome DevTools | Browser debugging protocol |

## Pre-Flight Test

### Test Inside Container

```bash
# Enter the running container
docker exec -it agent-zero-browser bash

# Run the comprehensive test script
./tests/test_docker_browser_service.sh

# If test script not found, run individual checks:
which Xvfb x11vnc websockify chromium-browser
ls -la /usr/share/novnc/
```

### Test From Host

```bash
# Test Agent Zero UI
curl -I http://localhost:50001

# Test noVNC availability (after enabling interactive mode)
curl -I http://localhost:6080

# Test Chrome DevTools (after browser service starts)
curl http://localhost:9222/json
```

## Configuration Steps

### 1. Enable Interactive Mode

1. Open Agent Zero: http://localhost:50001
2. Navigate to **Settings** → **Browser Model**
3. Enable **"Interactive Mode"** toggle
4. Configure ports (defaults should work):
   - VNC Port: 5900
   - noVNC Web Port: 6080  
   - Chrome DevTools Port: 9222
5. **Save Settings**

### 2. Verify Browser Service Startup

Check container logs:
```bash
docker logs agent-zero-browser

# Look for these messages:
# "Installing browser service dependencies..."
# "Browser service started successfully!"
```

### 3. Test Manual Control

1. Start a browser agent task: `"Navigate to google.com"`
2. Look for **"Take Manual Control"** button in browser messages
3. Click button to open VNC modal
4. Verify you can interact with the browser

## Troubleshooting Docker Issues

### Issue: Browser Service Won't Start

**Symptoms:**
- "Take Manual Control" button doesn't appear
- VNC connection refused
- Chrome DevTools not accessible

**Diagnosis:**
```bash
# Check if processes are running
docker exec agent-zero-browser ps aux | grep -E "(Xvfb|x11vnc|chrome)"

# Check port binding
docker exec agent-zero-browser netstat -tuln | grep -E "(5900|6080|9222)"

# Check browser service logs
docker exec agent-zero-browser cat /var/log/browser_service.log
```

**Solutions:**
1. **Restart browser service:**
   ```bash
   docker exec agent-zero-browser /exe/run_browser_service.sh
   ```

2. **Check permissions:**
   ```bash
   docker exec agent-zero-browser ls -la /exe/run_browser_service.sh
   docker exec agent-zero-browser chmod +x /exe/run_browser_service.sh
   ```

3. **Verify dependencies:**
   ```bash
   docker exec agent-zero-browser ./tests/test_docker_browser_service.sh
   ```

### Issue: VNC Connection Failed

**Symptoms:**
- Modal opens but shows connection error
- "Connection refused" in VNC client
- Blank screen in noVNC

**Solutions:**
1. **Check VNC server:**
   ```bash
   docker exec agent-zero-browser pgrep x11vnc
   docker exec agent-zero-browser x11vnc -display :99 -nopw -forever -shared -rfbport 5900 -bg
   ```

2. **Restart websockify:**
   ```bash
   docker exec agent-zero-browser pkill websockify
   docker exec agent-zero-browser websockify --web /usr/share/novnc 6080 localhost:5900 &
   ```

3. **Check display:**
   ```bash
   docker exec agent-zero-browser echo $DISPLAY
   docker exec agent-zero-browser pgrep Xvfb
   ```

### Issue: Chrome Won't Start

**Symptoms:**
- Chrome DevTools not accessible
- Browser appears but doesn't respond
- CDP connection failed

**Solutions:**
1. **Check Chrome process:**
   ```bash
   docker exec agent-zero-browser pgrep chrome
   docker exec agent-zero-browser pgrep chromium
   ```

2. **Manual Chrome start:**
   ```bash
   docker exec agent-zero-browser chromium-browser \
     --remote-debugging-port=9222 \
     --remote-debugging-address=0.0.0.0 \
     --no-sandbox \
     --disable-gpu \
     --disable-dev-shm-usage &
   ```

3. **Check user data directory:**
   ```bash
   docker exec agent-zero-browser ls -la /tmp/browser_profile_manual/
   docker exec agent-zero-browser rm -rf /tmp/browser_profile_manual/
   ```

### Issue: Port Conflicts

**Symptoms:**
- "Port already in use" errors
- Container fails to start
- Services can't bind to ports

**Solutions:**
1. **Check host ports:**
   ```bash
   netstat -tuln | grep -E "(5900|6080|9222|50001)"
   lsof -i :5900
   ```

2. **Use different ports:**
   ```bash
   docker run -d \
     -p 50002:80 \
     -p 5901:5900 \
     -p 6081:6080 \
     -p 9223:9222 \
     agent-zero-bc
   ```

3. **Stop conflicting services:**
   ```bash
   # Stop any existing VNC servers
   sudo pkill x11vnc
   sudo pkill Xvfb
   ```

## Docker Environment Variables

### Set Custom Ports

```bash
docker run -d \
  -e VNC_PORT=5901 \
  -e NOVNC_PORT=6081 \
  -e CDP_PORT=9223 \
  -p 50001:80 \
  -p 5901:5901 \
  -p 6081:6081 \
  -p 9223:9223 \
  agent-zero-bc
```

### Enable Debug Mode

```bash
docker run -d \
  -e DEBUG=true \
  -e BROWSER_DEBUG=true \
  -p 50001:80 \
  -p 5900:5900 \
  -p 6080:6080 \
  -p 9222:9222 \
  agent-zero-bc
```

## Performance Optimization

### Resource Limits

```bash
# Set memory and CPU limits
docker run -d \
  --memory=4g \
  --cpus=2 \
  -p 50001:80 \
  -p 5900:5900 \
  -p 6080:6080 \
  -p 9222:9222 \
  agent-zero-bc
```

### Persistent Storage

```bash
# Mount volume for browser data persistence
docker run -d \
  -v agent-zero-browser-data:/tmp/browser_profile_manual \
  -p 50001:80 \
  -p 5900:5900 \
  -p 6080:6080 \
  -p 9222:9222 \
  agent-zero-bc
```

## Security Considerations

### Production Deployment

```bash
# Run with restricted capabilities
docker run -d \
  --cap-drop=ALL \
  --cap-add=SYS_ADMIN \
  --security-opt=no-new-privileges \
  -p 50001:80 \
  -p 5900:5900 \
  -p 6080:6080 \
  -p 9222:9222 \
  agent-zero-bc
```

### Network Isolation

```bash
# Create custom network
docker network create agent-zero-net

# Run with custom network
docker run -d \
  --network=agent-zero-net \
  -p 50001:80 \
  -p 5900:5900 \
  -p 6080:6080 \
  -p 9222:9222 \
  agent-zero-bc
```

## Validation Checklist

After setup, verify these items work:

### Basic Functionality
- [ ] Container starts without errors
- [ ] Agent Zero UI accessible at http://localhost:50001
- [ ] Interactive mode toggle works in settings
- [ ] Browser agent can start tasks

### Browser Control
- [ ] "Take Manual Control" button appears on browser messages
- [ ] VNC modal opens when button clicked
- [ ] Browser is visible and interactive in VNC
- [ ] Control can be released and agent resumes

### Network Connectivity
- [ ] VNC client can connect to port 5900
- [ ] noVNC accessible at http://localhost:6080
- [ ] Chrome DevTools accessible at http://localhost:9222
- [ ] No firewall blocking ports

### Performance
- [ ] VNC response is smooth (< 500ms lag)
- [ ] Browser startup completes in < 10 seconds
- [ ] Memory usage stable during manual control
- [ ] No process leaks after control sessions

## Next Steps

1. **Complete Setup Test:**
   ```bash
   docker exec agent-zero-browser ./tests/test_docker_browser_service.sh
   ```

2. **Run Full Workflow Test:**
   - Start browser task
   - Take manual control
   - Perform actions
   - Release control
   - Verify agent continues

3. **Configure for Production:**
   - Set up reverse proxy for HTTPS
   - Configure authentication
   - Set up monitoring
   - Plan backup strategy

## Support

If you encounter issues:

1. **Check logs:** `docker logs agent-zero-browser`
2. **Run diagnostics:** `docker exec agent-zero-browser ./tests/test_docker_browser_service.sh`
3. **Verify ports:** `docker port agent-zero-browser`
4. **Test connectivity:** Use provided curl commands
5. **Report issues:** Include Docker version, host OS, and full error logs