# Browser Manual Control Demo

## Complete Workflow Demonstration

This document provides a step-by-step demonstration of the Browser Manual Control feature, showcasing all three stories working together.

## Setup

1. **Build and Run Agent Zero with Interactive Mode:**
   ```bash
   # Build the enhanced Docker image
   docker build -f docker/run/Dockerfile -t agent-zero-bc . --build-arg BRANCH=main
   
   # Run with all necessary ports
   docker run -p 50001:80 -p 5900:5900 -p 6080:6080 -p 9222:9222 agent-zero-bc
   ```

2. **Enable Interactive Mode:**
   - Navigate to http://localhost:50001
   - Go to Settings → Browser Model
   - Toggle "Enable Interactive Mode" ON
   - Save settings

## Demo Scenario: Online Account Setup

**Task:** "Help me create an account on example.com and fill out my profile"

### Phase 1: Autonomous Navigation (Story 1 in Action)

1. **Start the Task:**
   ```
   User: "Browse to example.com and help me create an account"
   ```

2. **Observe Browser Service:**
   - Browser agent starts in headed mode (if interactive mode enabled)
   - VNC service running in background
   - Control button appears on browser agent messages
   - Agent navigates to example.com autonomously

### Phase 2: Manual Intervention (Story 2 in Action)

3. **Encounter CAPTCHA:**
   - Agent reaches registration form
   - CAPTCHA appears (agent cannot solve automatically)
   - User sees "Take Manual Control" button

4. **Take Control:**
   - Click "Take Manual Control" button
   - VNC modal opens with live browser view
   - Timer starts counting
   - Agent execution pauses with message: "🖱️ Manual control activated"

5. **Manual Actions:**
   - Solve CAPTCHA manually
   - Fill in complex form fields (if needed)
   - Complete any human verification

6. **Release Control:**
   - Click "Release Control" button
   - Modal closes
   - Agent resumes with message: "🤖 Manual control released"

### Phase 3: State Synchronization (Story 3 in Action)

7. **Agent Adaptation:**
   - Agent detects form has been filled
   - State synchronized: "📄 State synchronized. Current page: Registration Complete"
   - Agent continues with updated context

8. **Task Completion:**
   - Agent recognizes account creation was successful
   - Continues to next phase (profile setup)
   - Seamlessly handles both autonomous and manual-assisted work

## Key Features Demonstrated

### Story 1: Browser Service Infrastructure ✅

- **Headed Browser:** Browser runs with full GUI in Docker
- **VNC Access:** Remote control through web interface  
- **CDP Integration:** Browser-use connects via Chrome DevTools Protocol
- **Fallback Support:** Graceful fallback to headless mode if service fails

### Story 2: Frontend Control Integration ✅

- **Smart Button:** Appears only when interactive mode available
- **Professional Modal:** VNC client with timer and controls
- **State Indicators:** Shows when control is active vs available
- **CSRF Protection:** Secure API endpoints for control management

### Story 3: State Synchronization ✅

- **Pause/Resume:** Agent pauses during manual control
- **State Sync:** Browser state updated after manual changes
- **Seamless Handoff:** Smooth transition between autonomous and manual modes
- **Context Awareness:** Agent adapts to changes made during manual control

## Advanced Scenarios

### Multi-Step Authentication

1. **Initial Login:** Agent navigates to login page
2. **Manual Auth:** User handles 2FA/complex authentication  
3. **Continued Flow:** Agent continues with authenticated session

### Complex Form Handling

1. **Form Detection:** Agent identifies complex form
2. **Selective Control:** User takes control for specific fields
3. **Autonomous Completion:** Agent handles simple fields automatically

### Error Recovery

1. **Agent Encounters Error:** Navigation fails or times out
2. **Manual Recovery:** User takes control to resolve issue
3. **Resumed Operation:** Agent continues from corrected state

## Testing Checklist

Use this checklist to verify all features work correctly:

### Basic Functionality
- [ ] Interactive mode enables/disables correctly
- [ ] Control button appears on browser messages
- [ ] VNC modal opens and connects
- [ ] Agent pauses during manual control
- [ ] Agent resumes after control release

### State Management
- [ ] Page changes detected after manual control
- [ ] Form fields recognized after manual filling
- [ ] URL changes synchronized
- [ ] Screenshots updated after manual actions

### User Experience
- [ ] Timer shows elapsed control time
- [ ] Control button states (available/active) correct
- [ ] Modal responsive on different screen sizes
- [ ] No JavaScript errors in console

### Error Handling
- [ ] VNC connection failures handled gracefully
- [ ] Browser service unavailable shows appropriate message
- [ ] Control timeout handled properly
- [ ] Agent task cancellation during control works

## Troubleshooting

### Common Issues

**Control Button Not Appearing:**
- Verify interactive mode is enabled in settings
- Check browser service is running (VNC ports accessible)
- Confirm browser agent is using headed mode

**VNC Connection Failed:**
- Check ports 5900, 6080, 9222 are exposed and accessible
- Verify browser service started correctly in Docker
- Check firewall settings

**Agent Not Pausing:**
- Verify manual control API endpoints are working
- Check CSRF tokens are being sent correctly
- Confirm agent context data is being updated

**State Not Synchronizing:**
- Verify browser-use connection is stable
- Check CDP connection after manual control
- Confirm selector map is being rebuilt

### Debug Commands

```bash
# Check browser service status
docker exec -it [container] ps aux | grep -E "(Xvfb|x11vnc|chrome)"

# Test VNC connection
curl -I http://localhost:6080

# Test CDP connection  
curl http://localhost:9222/json

# Check API endpoints
curl -X POST -H "Content-Type: application/json" \
     -d '{"session_id":"test"}' \
     http://localhost:50001/browser_control_take
```

## Conclusion

The Browser Manual Control feature provides a seamless bridge between autonomous AI operation and human intervention, enabling Agent Zero to handle complex web interactions that would otherwise be impossible to automate.

**Key Benefits:**
- **Flexibility:** Handle any web interaction, even CAPTCHAs and complex auth
- **Efficiency:** Automate what can be automated, manual control only when needed
- **Reliability:** Graceful fallback and error recovery
- **User-Friendly:** Intuitive interface with clear visual feedback

**Use Cases:**
- Account creation with human verification
- Complex form filling with validation
- Multi-factor authentication flows  
- Error recovery and troubleshooting
- Training AI on new interaction patterns

This feature transforms Agent Zero from a purely autonomous system into a collaborative human-AI tool for web automation.