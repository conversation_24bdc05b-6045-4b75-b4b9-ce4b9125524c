<!DOCTYPE html>
<html>
<head>
    <title>Test Browser Control - Fixed</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #1a1a1a;
            color: #ffffff;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #444;
            border-radius: 8px;
            background-color: #2a2a2a;
        }
        button {
            background: linear-gradient(135deg, #4a90e2, #357abd);
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: linear-gradient(135deg, #5ba0f2, #4580cd);
        }
        .status {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
            background-color: #333;
        }
        .success { background-color: #2d5a2d; }
        .error { background-color: #5a2d2d; }
        .info { background-color: #2d4a5a; }
    </style>
</head>
<body>
    <h1>Browser Control Test - Fixed Version</h1>
    
    <div class="test-section">
        <h2>Browser Control Button Test</h2>
        <p>This tests the fixed browser control functionality.</p>
        <button onclick="testBrowserControlFixed()">🖥️ Test Browser Control (Fixed)</button>
        <div id="status" class="status"></div>
    </div>
    
    <div class="test-section">
        <h2>Mock API Tests</h2>
        <p>Test the browser control logic with mock data.</p>
        <button onclick="testWithMockSessions()">Test with Mock Sessions</button>
        <button onclick="testWithNoSessions()">Test with No Sessions</button>
        <button onclick="testWithHeadlessSessions()">Test with Headless Sessions</button>
    </div>

    <script>
        // Mock Alpine store for testing
        window.Alpine = {
            store: () => ({
                appState: { csrfToken: 'test-token' }
            })
        };

        function updateStatus(message, type = 'info') {
            const status = document.getElementById('status');
            status.textContent = message;
            status.className = `status ${type}`;
        }

        // Mock fetch for testing
        const originalFetch = window.fetch;
        let mockMode = null;

        function mockFetch(url, options) {
            if (mockMode === 'sessions') {
                if (url.includes('browser_sessions')) {
                    return Promise.resolve({
                        ok: true,
                        json: () => Promise.resolve({
                            status: 'success',
                            sessions: [
                                {
                                    session_id: 'test-session-1',
                                    agent_name: 'Test Agent 1',
                                    agent_no: 1,
                                    vnc_url: 'http://localhost:6080/vnc.html',
                                    interactive_available: true
                                }
                            ]
                        })
                    });
                }
            } else if (mockMode === 'no-sessions') {
                if (url.includes('browser_sessions')) {
                    return Promise.resolve({
                        ok: true,
                        json: () => Promise.resolve({
                            status: 'success',
                            sessions: []
                        })
                    });
                }
                if (url.includes('browser_start_service')) {
                    return Promise.resolve({
                        ok: true,
                        json: () => Promise.resolve({
                            status: 'success',
                            novnc_url: 'http://localhost:6080/vnc.html'
                        })
                    });
                }
            } else if (mockMode === 'headless') {
                if (url.includes('browser_sessions')) {
                    return Promise.resolve({
                        ok: true,
                        json: () => Promise.resolve({
                            status: 'success',
                            sessions: [
                                {
                                    session_id: 'headless-session-1',
                                    agent_name: 'Headless Agent 1',
                                    agent_no: 1,
                                    session_active: true,
                                    mode: 'headless',
                                    interactive_available: false,
                                    interactive_fallback: true,
                                    interactive_error: 'X11 components not available on macOS'
                                }
                            ]
                        })
                    });
                }
            }
            
            return originalFetch(url, options);
        }

        // Simplified version of the browser control logic for testing
        async function handleBrowserControlClick() {
            try {
                updateStatus('Checking for browser sessions...', 'info');
                
                const response = await fetch('/browser_sessions', {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-Token': 'test-token'
                    }
                });
                
                if (!response.ok) {
                    throw new Error(`Failed to get browser sessions: ${response.statusText}`);
                }
                
                const data = await response.json();
                const sessions = data.sessions || [];
                
                updateStatus(`Found ${sessions.length} session(s)`, 'info');
                
                if (sessions.length === 0) {
                    updateStatus('No sessions found, trying to start browser service...', 'info');
                    // Would try to start service here
                    return;
                }
                
                const sessionsWithVnc = sessions.filter(session => session.vnc_url);
                const headlessSessions = sessions.filter(session => session.session_active && !session.vnc_url);
                
                if (sessionsWithVnc.length === 0 && headlessSessions.length > 0) {
                    let message = `Found ${headlessSessions.length} headless session(s). `;
                    if (headlessSessions.some(s => s.interactive_fallback)) {
                        message += 'Interactive mode failed (likely macOS without Docker).';
                    }
                    updateStatus(message, 'error');
                    return;
                }
                
                if (sessionsWithVnc.length > 0) {
                    const session = sessionsWithVnc[0];
                    updateStatus(`Would open VNC for session: ${session.agent_name}`, 'success');
                } else {
                    updateStatus('No VNC-enabled sessions found', 'error');
                }
                
            } catch (error) {
                updateStatus(`Error: ${error.message}`, 'error');
            }
        }

        function testBrowserControlFixed() {
            mockMode = null;
            window.fetch = originalFetch;
            handleBrowserControlClick();
        }

        function testWithMockSessions() {
            mockMode = 'sessions';
            window.fetch = mockFetch;
            handleBrowserControlClick();
        }

        function testWithNoSessions() {
            mockMode = 'no-sessions';
            window.fetch = mockFetch;
            handleBrowserControlClick();
        }

        function testWithHeadlessSessions() {
            mockMode = 'headless';
            window.fetch = mockFetch;
            handleBrowserControlClick();
        }
    </script>
</body>
</html>
