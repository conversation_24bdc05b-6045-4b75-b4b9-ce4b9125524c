<!DOCTYPE html>
<html>
<head>
    <title>Test Browser Control</title>
    <script>
        function testBrowserControl() {
            // Open directly to noVNC
            openBrowserControl('test-session', 'http://localhost:6080/vnc.html');
        }
        
        // Simple mock for the openBrowserControl function
        function openBrowserControl(sessionId, vncUrl) {
            window.open(vncUrl, '_blank', 'width=1600,height=900');
        }
    </script>
</head>
<body>
    <h1>Test Browser Control</h1>
    <button onclick="testBrowserControl()">Open Browser Control</button>
    <br><br>
    <p>This will open the noVNC interface in a new window</p>
    <p>Make sure the Agent Zero container is running on localhost:6080</p>
</body>
</html>
