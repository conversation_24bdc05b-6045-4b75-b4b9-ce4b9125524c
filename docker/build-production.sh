#!/bin/bash
# Production Docker Build Script for Agent Zero with Browser Control
# This script builds a production-ready Docker image with VNC browser control

set -e

echo "🚀 Building Agent Zero Production Docker Image with Browser Control"
echo "=================================================================="

# Configuration
IMAGE_NAME="agent-zero-production"
TAG="latest"
DOCKERFILE="Dockerfile.production"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    print_error "Docker is not running. Please start Docker and try again."
    exit 1
fi

print_status "Docker is running ✓"

# Check if Dockerfile exists
if [ ! -f "$DOCKERFILE" ]; then
    print_error "Dockerfile not found at $DOCKERFILE"
    exit 1
fi

print_status "Dockerfile found ✓"

# Build the Docker image
print_status "Building Docker image: $IMAGE_NAME:$TAG"
print_status "This may take several minutes..."

docker build \
    -f "$DOCKERFILE" \
    -t "$IMAGE_NAME:$TAG" \
    --build-arg BUILDKIT_INLINE_CACHE=1 \
    . || {
    print_error "Docker build failed"
    exit 1
}

print_success "Docker image built successfully: $IMAGE_NAME:$TAG"

# Verify the image
print_status "Verifying Docker image..."

# Check if image exists
if ! docker image inspect "$IMAGE_NAME:$TAG" > /dev/null 2>&1; then
    print_error "Built image not found"
    exit 1
fi

print_success "Docker image verified ✓"

# Test the image with a quick startup
print_status "Testing image startup..."

CONTAINER_ID=$(docker run -d \
    --name "agent-zero-test-$$" \
    -p 50001:80 \
    -p 5900:5900 \
    -p 6080:6080 \
    -p 9222:9222 \
    "$IMAGE_NAME:$TAG" || {
    print_error "Failed to start test container"
    exit 1
})

print_status "Test container started: $CONTAINER_ID"

# Wait for container to be ready with improved health check
print_status "Waiting for container to initialize..."

# Enhanced health check with timeout and log parsing
timeout=60
health_check_passed=false

# Get short container ID for matching (first 12 chars)
CONTAINER_SHORT_ID=${CONTAINER_ID:0:12}

while [[ $timeout -gt 0 ]]; do
    # Check if container is still running
    if ! docker ps -q | grep -q "$CONTAINER_SHORT_ID"; then
        print_error "Container stopped unexpectedly"
        docker logs "$CONTAINER_ID"
        docker rm -f "$CONTAINER_ID" 2>/dev/null || true
        exit 1
    fi
    
    # Get recent logs to check for health indicators
    recent_logs=$(docker logs --tail=20 "$CONTAINER_ID" 2>&1)
    
    # Check for positive health indicators
    if echo "$recent_logs" | grep -q "Running on all addresses" && \
       (echo "$recent_logs" | grep -q "Waiting for Agent Zero settings file" || \
        echo "$recent_logs" | grep -q "Preload completed"); then
        health_check_passed=true
        break
    fi
    
    # Check for critical errors that indicate failure
    if echo "$recent_logs" | grep -qi "critical\|fatal\|error.*failed" && \
       ! echo "$recent_logs" | grep -q "Waiting for Agent Zero settings file"; then
        print_error "Container encountered critical errors during startup"
        docker logs "$CONTAINER_ID"
        docker rm -f "$CONTAINER_ID" 2>/dev/null || true
        exit 1
    fi
    
    sleep 2
    timeout=$((timeout-2))
    
    # Show progress every 10 seconds
    if [[ $((timeout % 10)) -eq 0 ]]; then
        print_status "Still waiting for container to be ready... (${timeout}s remaining)"
    fi
done

if [[ "$health_check_passed" != "true" ]]; then
    print_error "Container did not reach healthy state within timeout"
    docker logs "$CONTAINER_ID"
    docker rm -f "$CONTAINER_ID" 2>/dev/null || true
    exit 1
fi

print_success "Container health check passed ✓"

# Test if web interface is accessible
print_status "Testing web interface accessibility..."
if curl -f -s http://localhost:50001 > /dev/null; then
    print_success "Web interface is accessible ✓"
else
    print_warning "Web interface not immediately accessible (may still be starting up)"
fi

# Clean up test container
print_status "Cleaning up test container..."
docker stop "$CONTAINER_ID" > /dev/null
docker rm "$CONTAINER_ID" > /dev/null

print_success "Test container cleaned up ✓"

# Display usage instructions
echo ""
echo "🎉 Production Docker Image Ready!"
echo "================================="
echo ""
echo "Image: $IMAGE_NAME:$TAG"
echo ""
echo "To run the production container:"
echo ""
echo "docker run -d \\"
echo "  --name agent-zero-production \\"
echo "  -p 50001:80 \\"
echo "  -p 5900:5900 \\"
echo "  -p 6080:6080 \\"
echo "  -p 9222:9222 \\"
echo "  -v \$(pwd)/data:/a0/data \\"
echo "  -v \$(pwd)/tmp:/a0/tmp \\"
echo "  $IMAGE_NAME:$TAG"
echo ""
echo "Port mappings:"
echo "  50001 -> Agent Zero Web Interface"
echo "  5900  -> VNC Server (for VNC clients)"
echo "  6080  -> noVNC Web Interface"
echo "  9222  -> Chrome DevTools Protocol"
echo ""
echo "After starting:"
echo "  1. Visit http://localhost:50001 for Agent Zero"
echo "  2. Enable 'Interactive Browser Mode' in settings"
echo "  3. Use browser agent - manual control will be available"
echo "  4. VNC access at http://localhost:6080/vnc.html"
echo ""
echo "Volume mounts (recommended):"
echo "  ./data -> /a0/data (persistent data)"
echo "  ./tmp  -> /a0/tmp  (settings and temporary files)"
echo ""

print_success "Build complete! 🚀"
