#!/bin/bash

echo "Running initialization script..."

# branch from parameter
if [ -z "$1" ]; then
    echo "Error: Branch parameter is empty. Please provide a valid branch name."
    exit 1
fi
BRANCH="$1"

# Copy all contents from persistent /per to root directory (/) without overwriting (if /per exists)
# Skip for local builds since we already have the files we need
if [ "$BRANCH" != "local" ] && [ -d "/per" ] && [ "$(ls -A /per 2>/dev/null)" ]; then
    echo "Copying files from /per to root directory..."
    if ! cp -r --no-preserve=ownership,mode /per/* / 2>/dev/null; then
        echo "Warning: Some files from /per could not be copied, continuing anyway..."
    fi
else
    echo "Skipping /per copy operation (local build or no /per directory)"
fi

# allow execution of /root/.bashrc and /root/.profile (if they exist)
if [ -f "/root/.bashrc" ]; then
    chmod 444 /root/.bashrc
fi
if [ -f "/root/.profile" ]; then
    chmod 444 /root/.profile
fi

# update package list to save time later
apt-get update > /dev/null 2>&1 &

# let supervisord handle the services
exec /usr/bin/supervisord -c /etc/supervisor/conf.d/supervisord.conf
