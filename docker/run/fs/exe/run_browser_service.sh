#!/bin/bash
# Browser Service Startup Script for Manual Control
# This script starts the headed browser with VNC access for user intervention

set -e

echo "Starting Browser Service for Manual Control in Docker..."

# Configuration
DISPLAY_NUM=99
VNC_PORT=${VNC_PORT:-5900}
NOVNC_PORT=${NOVNC_PORT:-6080}
CDP_PORT=${CDP_PORT:-9222}
SCREEN_WIDTH=${SCREEN_WIDTH:-1920}
SCREEN_HEIGHT=${SCREEN_HEIGHT:-1080}

# Docker-specific environment setup
export HOME=/tmp
export USER=root
export DISPLAY=:$DISPLAY_NUM

# Check if already running
if pgrep -x "Xvfb" > /dev/null; then
    echo "Xvfb already running"
else
    echo "Starting Xvfb on display :$DISPLAY_NUM..."
    Xvfb :$DISPLAY_NUM -screen 0 ${SCREEN_WIDTH}x${SCREEN_HEIGHT}x24 -ac +extension GLX +render -noreset &
    sleep 2
fi

# Export display
export DISPLAY=:$DISPLAY_NUM

# Start window manager (fluxbox)
if pgrep -x "fluxbox" > /dev/null; then
    echo "Fluxbox already running"
else
    echo "Starting Fluxbox window manager..."
    fluxbox -display :$DISPLAY_NUM &
    sleep 1
fi

# Start VNC server
if pgrep -x "x11vnc" > /dev/null; then
    echo "VNC server already running"
else
    echo "Starting VNC server on port $VNC_PORT..."
    x11vnc -display :$DISPLAY_NUM -nopw -forever -shared -rfbport $VNC_PORT -bg
    sleep 1
fi

# Start noVNC web server
if pgrep -f "websockify.*$NOVNC_PORT" > /dev/null; then
    echo "noVNC already running"
else
    echo "Starting noVNC web server on port $NOVNC_PORT..."
    websockify --web /usr/share/novnc $NOVNC_PORT localhost:$VNC_PORT &
    sleep 1
fi

# Start Chrome/Chromium with remote debugging
if pgrep -f "chrome.*remote-debugging-port=$CDP_PORT" > /dev/null; then
    echo "Chrome already running with debugging"
else
    echo "Starting Chrome with remote debugging on port $CDP_PORT..."
    
    # Find Chrome binary
    CHROME_BIN=""
    for chrome in chromium chromium-browser google-chrome google-chrome-stable; do
        if command -v $chrome &> /dev/null; then
            CHROME_BIN=$chrome
            break
        fi
    done
    
    if [ -z "$CHROME_BIN" ]; then
        echo "ERROR: Chrome/Chromium not found!"
        exit 1
    fi
    
    $CHROME_BIN \
        --remote-debugging-port=$CDP_PORT \
        --remote-debugging-address=0.0.0.0 \
        --user-data-dir=/tmp/browser_profile_manual \
        --no-first-run \
        --no-default-browser-check \
        --disable-default-apps \
        --disable-dev-shm-usage \
        --disable-gpu \
        --no-sandbox \
        --disable-setuid-sandbox \
        --disable-background-timer-throttling \
        --disable-backgrounding-occluded-windows \
        --disable-renderer-backgrounding \
        --window-size=${SCREEN_WIDTH},${SCREEN_HEIGHT} \
        --window-position=0,0 \
        --enable-logging \
        --log-level=0 \
        about:blank &
    
    sleep 3
fi

echo "Browser Service started successfully!"
echo "----------------------------------------"
echo "VNC Server: vnc://localhost:$VNC_PORT"
echo "noVNC Web: http://localhost:$NOVNC_PORT/vnc.html"
echo "Chrome DevTools: http://localhost:$CDP_PORT"
echo "----------------------------------------"

# Keep script running
tail -f /dev/null