#!/bin/bash
set -e

# install playwright - moved to install A0
# bash /ins/install_playwright.sh "$@"

# searxng - moved to base image
# bash /ins/install_searxng.sh "$@"

# Install browser service dependencies for manual control
echo "Installing browser service dependencies..."
apt-get update && apt-get install -y \
    xvfb \
    x11vnc \
    websockify \
    fluxbox \
    chromium \
    && rm -rf /var/lib/apt/lists/*

# Create novnc directory structure for compatibility
echo "Setting up VNC web interface..."
mkdir -p /usr/share/novnc
echo '<!DOCTYPE html><html><head><title>noVNC</title></head><body><h1>Agent Zero Built-in VNC</h1><p>VNC web interface is built into Agent Zero.</p></body></html>' > /usr/share/novnc/vnc.html

echo "Browser service dependencies installed successfully."