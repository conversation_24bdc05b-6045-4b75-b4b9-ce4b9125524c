#!/bin/bash
# Production Docker Run Script for Agent Zero with Browser Control
# This script runs the production Docker container with proper configuration

set -e

# Configuration
IMAGE_NAME="agent-zero-production"
TAG="latest"
CONTAINER_NAME="agent-zero-production"

# Default ports
WEB_PORT=50001
VNC_PORT=5900
NOVNC_PORT=6080
CDP_PORT=9222

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to show usage
show_usage() {
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  -p, --web-port PORT     Web interface port (default: $WEB_PORT)"
    echo "  -v, --vnc-port PORT     VNC server port (default: $VNC_PORT)"
    echo "  -n, --novnc-port PORT   noVNC web port (default: $NOVNC_PORT)"
    echo "  -c, --cdp-port PORT     Chrome DevTools port (default: $CDP_PORT)"
    echo "  -d, --detach            Run in background (detached mode)"
    echo "  -i, --interactive       Run in interactive mode (default)"
    echo "  --stop                  Stop the running container"
    echo "  --restart               Restart the container"
    echo "  --logs                  Show container logs"
    echo "  --status                Show container status"
    echo "  -h, --help              Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0                      # Run interactively with default ports"
    echo "  $0 -d                   # Run in background"
    echo "  $0 -p 8080 -d           # Run in background on port 8080"
    echo "  $0 --stop               # Stop the container"
    echo "  $0 --logs               # Show logs"
}

# Parse command line arguments
DETACHED=false
ACTION="run"

while [[ $# -gt 0 ]]; do
    case $1 in
        -p|--web-port)
            WEB_PORT="$2"
            shift 2
            ;;
        -v|--vnc-port)
            VNC_PORT="$2"
            shift 2
            ;;
        -n|--novnc-port)
            NOVNC_PORT="$2"
            shift 2
            ;;
        -c|--cdp-port)
            CDP_PORT="$2"
            shift 2
            ;;
        -d|--detach)
            DETACHED=true
            shift
            ;;
        -i|--interactive)
            DETACHED=false
            shift
            ;;
        --stop)
            ACTION="stop"
            shift
            ;;
        --restart)
            ACTION="restart"
            shift
            ;;
        --logs)
            ACTION="logs"
            shift
            ;;
        --status)
            ACTION="status"
            shift
            ;;
        -h|--help)
            show_usage
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
done

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    print_error "Docker is not running. Please start Docker and try again."
    exit 1
fi

# Handle different actions
case $ACTION in
    "stop")
        print_status "Stopping Agent Zero container..."
        if docker stop "$CONTAINER_NAME" 2>/dev/null; then
            print_success "Container stopped"
        else
            print_warning "Container was not running"
        fi
        exit 0
        ;;
    "restart")
        print_status "Restarting Agent Zero container..."
        docker stop "$CONTAINER_NAME" 2>/dev/null || true
        docker rm "$CONTAINER_NAME" 2>/dev/null || true
        # Continue to run section
        ;;
    "logs")
        print_status "Showing container logs..."
        docker logs -f "$CONTAINER_NAME" 2>/dev/null || {
            print_error "Container not found or not running"
            exit 1
        }
        exit 0
        ;;
    "status")
        print_status "Container status:"
        if docker ps -a --filter "name=$CONTAINER_NAME" --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}" | grep -q "$CONTAINER_NAME"; then
            docker ps -a --filter "name=$CONTAINER_NAME" --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"
        else
            print_warning "Container not found"
        fi
        exit 0
        ;;
esac

# Check if image exists
if ! docker image inspect "$IMAGE_NAME:$TAG" > /dev/null 2>&1; then
    print_error "Docker image $IMAGE_NAME:$TAG not found"
    print_status "Please build the image first with: ./docker/build-production.sh"
    exit 1
fi

# Stop and remove existing container if it exists
if docker ps -a --filter "name=$CONTAINER_NAME" --format "{{.Names}}" | grep -q "^$CONTAINER_NAME$"; then
    print_status "Removing existing container..."
    docker stop "$CONTAINER_NAME" 2>/dev/null || true
    docker rm "$CONTAINER_NAME" 2>/dev/null || true
fi

# Create data and tmp directories if they don't exist
mkdir -p data tmp

print_status "Starting Agent Zero Production Container"
print_status "========================================"
print_status "Image: $IMAGE_NAME:$TAG"
print_status "Container: $CONTAINER_NAME"
print_status "Web Interface: http://localhost:$WEB_PORT"
print_status "VNC Server: localhost:$VNC_PORT"
print_status "noVNC Web: http://localhost:$NOVNC_PORT/vnc.html"
print_status "Chrome DevTools: http://localhost:$CDP_PORT"
print_status "Mode: $([ "$DETACHED" = true ] && echo "Detached" || echo "Interactive")"

# Build docker run command
DOCKER_CMD="docker run"

if [ "$DETACHED" = true ]; then
    DOCKER_CMD="$DOCKER_CMD -d"
else
    DOCKER_CMD="$DOCKER_CMD -it"
fi

DOCKER_CMD="$DOCKER_CMD \
    --name $CONTAINER_NAME \
    -p $WEB_PORT:80 \
    -p $VNC_PORT:5900 \
    -p $NOVNC_PORT:6080 \
    -p $CDP_PORT:9222 \
    -v $(pwd)/data:/a0/data \
    -v $(pwd)/tmp:/a0/tmp \
    --restart unless-stopped \
    $IMAGE_NAME:$TAG"

# Run the container
print_status "Executing: $DOCKER_CMD"
eval $DOCKER_CMD || {
    print_error "Failed to start container"
    exit 1
}

if [ "$DETACHED" = true ]; then
    print_success "Container started in background"
    print_status "Access Agent Zero at: http://localhost:$WEB_PORT"
    print_status "View logs with: $0 --logs"
    print_status "Stop with: $0 --stop"
else
    print_status "Container running in interactive mode"
    print_status "Press Ctrl+C to stop"
fi

print_success "Agent Zero is ready! 🚀"
